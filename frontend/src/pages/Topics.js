import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  Tooltip,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  Topic as TopicIcon,
  Settings,
  Search,
  Clear,
  Message as MessageIcon,
  Refresh,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import { useDebounce } from '../hooks/useDebounce';

// Utility function to format numbers
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const CreateTopicDialog = ({ open, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    numPartitions: 1,
    replicationFactor: 1,
    configs: [],
  });

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      toast.error('Topic name is required');
      return;
    }
    onSubmit(formData);
    setFormData({ name: '', numPartitions: 1, replicationFactor: 1, configs: [] });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Topic</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          <TextField
            fullWidth
            label="Topic Name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
          <TextField
            fullWidth
            label="Number of Partitions"
            type="number"
            value={formData.numPartitions}
            onChange={(e) => handleChange('numPartitions', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
          <TextField
            fullWidth
            label="Replication Factor"
            type="number"
            value={formData.replicationFactor}
            onChange={(e) => handleChange('replicationFactor', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">
          Create Topic
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const TopicCard = ({ topic, onEdit, onDelete, onView, onConfigure, onLoadMessageCount }) => {
  const [isLoadingCount, setIsLoadingCount] = useState(false);
  const [messageCount, setMessageCount] = useState(topic.totalMessages);
  const [partitionDetails, setPartitionDetails] = useState(topic.partitionDetails);

  const handleLoadMessageCount = async () => {
    if (messageCount !== undefined && !isLoadingCount) return; // Already loaded
    
    setIsLoadingCount(true);
    try {
      const response = await topicsApi.getMessageCount(topic.name);
      setMessageCount(response.data.totalMessages);
      setPartitionDetails(response.data.partitionDetails);
      toast.success(`Message count loaded for ${topic.name}`);
    } catch (error) {
      toast.error(`Failed to load message count: ${error.message}`);
    } finally {
      setIsLoadingCount(false);
    }
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TopicIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography 
            variant="h6" 
            component="h2"
            sx={{ 
              fontSize: { xs: '1.125rem', sm: '1.25rem' },
              fontWeight: 600,
              wordBreak: 'break-word',
            }}
          >
            {topic.name}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <Chip
            label={`${topic.partitions} partitions`}
            size="small"
            color="primary"
            variant="outlined"
          />
          <Chip
            label={`${topic.partitionDetails?.length || 0} replicas`}
            size="small"
            color="secondary"
            variant="outlined"
          />
        </Box>

        {/* Message Count Section */}
        <Box sx={{ mb: 2 }}>
          <Divider sx={{ mb: 1 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <MessageIcon sx={{ color: 'success.main', fontSize: 20 }} />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Total Messages
              </Typography>
              {messageCount !== undefined ? (
                <Typography variant="h6" color="success.main">
                  {formatNumber(messageCount)}
                </Typography>
              ) : (
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={isLoadingCount ? <CircularProgress size={16} /> : <Refresh />}
                  onClick={handleLoadMessageCount}
                  disabled={isLoadingCount}
                  sx={{ mt: 0.5 }}
                >
                  {isLoadingCount ? 'Loading...' : 'Load Count'}
                </Button>
              )}
            </Box>
          </Box>
          
          {/* Partition Details */}
          {partitionDetails && partitionDetails.length > 0 && messageCount !== undefined && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                Messages per partition:
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {partitionDetails.map((partition) => (
                  <Tooltip 
                    key={partition.partitionId}
                    title={`Partition ${partition.partitionId}: ${partition.messageCount || 0} messages`}
                  >
                    <Chip
                      label={`P${partition.partitionId}: ${formatNumber(partition.messageCount || 0)}`}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.7rem' }}
                    />
                  </Tooltip>
                ))}
              </Box>
            </Box>
          )}
        </Box>

        <Box sx={{ 
          display: 'flex', 
          gap: { xs: 0.5, sm: 1 }, 
          mt: 'auto',
          flexWrap: 'wrap',
        }}>
          <Button
            size="small"
            startIcon={<Visibility />}
            onClick={() => onView(topic.name)}
            sx={{ 
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              minWidth: { xs: 'auto', sm: 'auto' },
              px: { xs: 1, sm: 2 },
            }}
          >
            View
          </Button>
          <IconButton
            size="small"
            onClick={() => onEdit(topic)}
            color="primary"
            sx={{ 
              width: { xs: 32, sm: 36 },
              height: { xs: 32, sm: 36 },
            }}
          >
            <Edit fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => onDelete(topic.name)}
            color="error"
            sx={{ 
              width: { xs: 32, sm: 36 },
              height: { xs: 32, sm: 36 },
            }}
          >
            <Delete fontSize="small" />
          </IconButton>
          <IconButton
            color="primary"
            onClick={() => onConfigure(topic.name)}
            title="Configure Topic"
            sx={{ 
              width: { xs: 32, sm: 36 },
              height: { xs: 32, sm: 36 },
            }}
          >
            <Settings fontSize="small" />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );
};

const Topics = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [topicToDelete, setTopicToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTopics, setFilteredTopics] = useState([]);
  const [isFiltering, setIsFiltering] = useState(false);

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Use the regular topics API without message counts for fast loading
  const { data: topics, isLoading, refetch: refetchTopics } = useQuery('topics', topicsApi.getAll, {
    // Remove automatic refetching to improve performance
    refetchInterval: false,
    // Only refetch when user explicitly requests it or when window regains focus
    refetchOnWindowFocus: true,
    // Cache data for 5 minutes to avoid unnecessary requests
    staleTime: 5 * 60 * 1000,
  });

  // Debounce search term to avoid excessive filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Asynchronous filtering to prevent UI blocking
  useEffect(() => {
    const performFiltering = async () => {
      if (!topics?.data) {
        setFilteredTopics([]);
        return;
      }

      if (!debouncedSearchTerm.trim()) {
        setFilteredTopics(topics.data);
        setIsFiltering(false);
        return;
      }

      setIsFiltering(true);

      // Use setTimeout to break the filtering into chunks and prevent UI blocking
      const filterInChunks = () => {
        return new Promise((resolve) => {
          const searchLower = debouncedSearchTerm.toLowerCase();
          const chunkSize = 50; // Process 50 topics at a time
          const result = [];
          let index = 0;

          const processChunk = () => {
            const endIndex = Math.min(index + chunkSize, topics.data.length);

            for (let i = index; i < endIndex; i++) {
              const topic = topics.data[i];
              if (topic.name.toLowerCase().includes(searchLower)) {
                result.push(topic);
              }
            }

            index = endIndex;

            if (index < topics.data.length) {
              // Process next chunk in next tick to keep UI responsive
              setTimeout(processChunk, 0);
            } else {
              resolve(result);
            }
          };

          processChunk();
        });
      };

      try {
        const filtered = await filterInChunks();
        setFilteredTopics(filtered);
      } catch (error) {
        console.error('Error filtering topics:', error);
        setFilteredTopics(topics.data);
      } finally {
        setIsFiltering(false);
      }
    };

    performFiltering();
  }, [topics?.data, debouncedSearchTerm]);

  const createMutation = useMutation(topicsApi.create, {
    onSuccess: () => {
      toast.success('Topic created successfully');
      queryClient.invalidateQueries('topics');
      setCreateDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Error creating topic: ${error.message}`);
    },
  });

  const deleteMutation = useMutation(topicsApi.delete, {
    onSuccess: () => {
      toast.success('Topic deleted successfully');
      queryClient.invalidateQueries('topics');
      setDeleteConfirmOpen(false);
      setTopicToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting topic: ${error.message}`);
    },
  });

  const handleCreateTopic = (topicData) => {
    createMutation.mutate(topicData);
  };

  const handleDeleteTopic = (topicName) => {
    setTopicToDelete(topicName);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (topicToDelete) {
      deleteMutation.mutate(topicToDelete);
    }
  };

  const handleViewTopic = (topicName) => {
    navigate(`/topics/${topicName}`);
  };

  const handleConfigureTopic = (topicName) => {
    navigate(`/topics/${topicName}`, { state: { activeTab: 3 } });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between', 
        alignItems: { xs: 'stretch', sm: 'center' }, 
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"}
          sx={{ 
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Topics
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexDirection: isSmallScreen ? 'column' : 'row' }}>
          <IconButton
            onClick={refetchTopics}
            disabled={isLoading}
            size={isSmallScreen ? "small" : "medium"}
            title="Refresh topics"
            sx={{
              color: 'primary.main',
              '&:hover': { backgroundColor: 'primary.light', color: 'white' }
            }}
          >
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
            size={isSmallScreen ? "small" : "medium"}
            fullWidth={isSmallScreen}
          >
            Create Topic
          </Button>
        </Box>
      </Box>

      {/* Performance Notice */}
      <Alert severity="info" sx={{ mb: { xs: 2, sm: 3 } }}>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
          <strong>Performance Note:</strong> Message counts are loaded on-demand to improve page load speed. 
          Click "Load Count" on any topic card to see its message statistics.
        </Typography>
      </Alert>

      {/* Search Bar */}
      <Box sx={{ mb: { xs: 2, sm: 3 } }}>
        <TextField
          fullWidth
          placeholder="Search topics..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isSmallScreen ? "small" : "medium"}
        />
      </Box>

      {/* Results Info */}
      {debouncedSearchTerm && (
        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {isFiltering
              ? `Searching for "${debouncedSearchTerm}"...`
              : `${filteredTopics.length} topic(s) found for "${debouncedSearchTerm}"`
            }
          </Typography>
          {isFiltering && <CircularProgress size={16} />}
        </Box>
      )}

      {topics?.data?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found. Create your first topic to get started.
        </Alert>
      ) : filteredTopics.length === 0 && debouncedSearchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found matching "{debouncedSearchTerm}". Try a different search term.
        </Alert>
      ) : isFiltering ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Filtering topics...
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {filteredTopics.map((topic) => (
            <Grid item xs={12} sm={6} md={4} key={topic.name}>
              <TopicCard
                topic={topic}
                onView={handleViewTopic}
                onEdit={() => {}}
                onDelete={handleDeleteTopic}
                onConfigure={handleConfigureTopic}
              />
            </Grid>
          ))}
        </Grid>
      )}

      <CreateTopicDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTopic}
      />

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete topic "{topicToDelete}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Topics; 