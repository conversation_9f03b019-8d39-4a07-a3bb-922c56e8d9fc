{"name": "react-window-infinite-loader", "version": "1.0.10", "description": "InfiniteLoader component inspired by react-virtualized but for use with react-window", "author": "<PERSON> <<EMAIL>> (https://github.com/bvaughn/)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/bvaughn/)"], "license": "MIT", "homepage": "https://github.com/bvaughn/react-window-infinite-loader/", "repository": {"type": "git", "url": "https://github.com/bvaughn/react-window-infinite-loader.git"}, "bugs": {"url": "https://github.com/bvaughn/react-window-infinite-loader/issues"}, "engines": {"node": ">8.0.0"}, "keywords": ["react", "reactjs", "virtual", "window", "windowed", "list", "scrolling", "infinite", "virtualized", "table", "grid", "spreadsheet"], "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "files": ["dist/*"], "scripts": {"flow": "flow", "precommit": "lint-staged", "prettier": "prettier --write '**/*.{js,json,css}'", "linc": "lint-staged", "lint": "eslint '**/*.js'", "test": "cross-env CI=1 react-scripts test --env=jsdom", "test:watch": "react-scripts test --env=jsdom", "build": "del dist && rollup -c", "start": "rollup -c -w", "prepare": "yarn run build", "predeploy": "cd example && yarn install && yarn run build", "deploy": "gh-pages -d example/build"}, "lint-staged": {"{example,src}/**/*.{js,json,css}": ["prettier --write", "git add"], "**/*.js": "eslint --max-warnings 0"}, "peerDependencies": {"react": "^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"babel-core": "^6.26.0", "babel-eslint": "^8.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "cross-env": "^5.1.4", "del-cli": "^1.1.0", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-config-react-app": "^2.1.0", "eslint-config-standard": "^11.0.0", "eslint-config-standard-react": "^6.0.0", "eslint-plugin-flowtype": "^2.47.1", "eslint-plugin-import": "^2.11.0", "eslint-plugin-jsx-a11y": "^5", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-react": "^7.7.0", "eslint-plugin-standard": "^3.0.1", "gh-pages": "^1.1.0", "lint-staged": "^7.0.5", "prettier": "^1.12.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-scripts": "^1.1.1", "react-test-renderer": "^17.0.1", "react-window": "^1.3.1", "rollup": "^0.59.3", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.2", "rollup-plugin-peer-deps-external": "^2.0.0", "rollup-plugin-postcss": "^1.1.0", "rollup-plugin-url": "^1.3.0"}}