{"name": "react-window", "version": "1.8.11", "description": "React components for efficiently rendering large, scrollable lists and tabular data", "author": "<PERSON> <<EMAIL>> (https://github.com/bvaughn/)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/bvaughn/)"], "license": "MIT", "homepage": "http://react-window.now.sh/", "repository": {"type": "git", "url": "https://github.com/bvaughn/react-window.git"}, "bugs": {"url": "https://github.com/bvaughn/react-window/issues"}, "engines": {"node": ">8.0.0"}, "keywords": ["react", "reactjs", "virtual", "window", "windowed", "list", "scrolling", "infinite", "virtualized", "table", "grid", "spreadsheet"], "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "files": ["dist", "src/*.js"], "sideEffects": false, "scripts": {"flow": "flow check --max-warnings=0 src && flow check website", "precommit": "lint-staged", "prettier": "prettier --write '**/*.{js,json,css}'", "linc": "lint-staged", "lint": "eslint '**/*.js'", "test": "cross-env CI=1 react-scripts test --env=jsdom", "test:watch": "react-scripts test --env=jsdom", "build:flow": "cp flow-template dist/index.cjs.js.flow && cp flow-template dist/index.esm.js.flow", "build:source": "rollup -c", "build": "del dist && mkdir dist && yarn build:flow && yarn build:source", "start": "rollup -c -w", "prepare": "yarn run build", "website:build": "cd website && yarn build", "website:deploy": "cd website && yarn deploy", "website:run": "cd website && yarn start"}, "lint-staged": {"{website,src}/**/*.{js,json,css}": ["prettier --write", "git add"], "**/*.js": "eslint --max-warnings 0"}, "dependencies": {"@babel/runtime": "^7.0.0", "memoize-one": ">=3.1.1 <6"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^9.0.0", "babel-plugin-annotate-pure-calls": "^0.3.0", "cross-env": "^5.1.4", "del-cli": "^1.1.0", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-config-react-app": "^2.1.0", "eslint-config-standard": "^11.0.0", "eslint-config-standard-react": "^6.0.0", "eslint-plugin-flowtype": "^2.47.1", "eslint-plugin-import": "^2.11.0", "eslint-plugin-jsx-a11y": "^5", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-react": "^7.7.0", "eslint-plugin-standard": "^3.0.1", "flow-bin": "^0.111.0", "gh-pages": "^1.1.0", "lint-staged": "^7.0.5", "prettier": "^1.12.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-is": "^17.0.1", "react-scripts": "^1.1.1", "react-test-renderer": "^17.0.1", "rollup": "^1.4.1", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^4.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.0"}}