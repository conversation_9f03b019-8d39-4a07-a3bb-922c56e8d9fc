[{"/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js": "1", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js": "2", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js": "3", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js": "5", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js": "6", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js": "7", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js": "8", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js": "9", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js": "10", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js": "11", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js": "12", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js": "13", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js": "14", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js": "15", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js": "16", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js": "17", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js": "18", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js": "19", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/App.js": "20", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/ProtectedRoute.js": "21", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Login.js": "22", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Navbar.js": "23", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Dashboard.js": "24", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Topics.js": "25", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroupDetail.js": "26", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js": "27", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Producer.js": "28", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/TopicDetail.js": "29", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ClusterInfo.js": "30", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Analytics.js": "31", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/MessageBrowser.js": "32", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Settings.js": "33", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Sidebar.js": "34", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/socket.js": "35", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/api.js": "36", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/hooks/useDebounce.js": "37"}, {"size": 3177, "mtime": 1751888199075, "results": "38", "hashOfConfig": "39"}, {"size": 6294, "mtime": 1751888799347, "results": "40", "hashOfConfig": "39"}, {"size": 6709, "mtime": 1751875530445, "results": "41", "hashOfConfig": "39"}, {"size": 8590, "mtime": 1751889099210, "results": "42", "hashOfConfig": "39"}, {"size": 6282, "mtime": 1751631936614, "results": "43", "hashOfConfig": "39"}, {"size": 14711, "mtime": 1751889035484, "results": "44", "hashOfConfig": "39"}, {"size": 8745, "mtime": 1751631930603, "results": "45", "hashOfConfig": "39"}, {"size": 24364, "mtime": 1751889065121, "results": "46", "hashOfConfig": "39"}, {"size": 9650, "mtime": 1751631928592, "results": "47", "hashOfConfig": "39"}, {"size": 7710, "mtime": 1751631934963, "results": "48", "hashOfConfig": "39"}, {"size": 18286, "mtime": 1751884544652, "results": "49", "hashOfConfig": "39"}, {"size": 7379, "mtime": 1751888900779, "results": "50", "hashOfConfig": "39"}, {"size": 3867, "mtime": 1751889014335, "results": "51", "hashOfConfig": "39"}, {"size": 4107, "mtime": 1751887209304, "results": "52", "hashOfConfig": "39"}, {"size": 1614, "mtime": 1751631890462, "results": "53", "hashOfConfig": "39"}, {"size": 7966, "mtime": 1751888244160, "results": "54", "hashOfConfig": "39"}, {"size": 384, "mtime": 1751876231921, "results": "55", "hashOfConfig": "39"}, {"size": 10563, "mtime": 1751882224072, "results": "56", "hashOfConfig": "39"}, {"size": 3177, "mtime": 1751972373749, "results": "57", "hashOfConfig": "58"}, {"size": 6294, "mtime": 1751889768472, "results": "59", "hashOfConfig": "58"}, {"size": 384, "mtime": 1751889768476, "results": "60", "hashOfConfig": "58"}, {"size": 7966, "mtime": 1751889768476, "results": "61", "hashOfConfig": "58"}, {"size": 7379, "mtime": 1751889768476, "results": "62", "hashOfConfig": "58"}, {"size": 8590, "mtime": 1751889768476, "results": "63", "hashOfConfig": "58"}, {"size": 14995, "mtime": 1751977927209, "results": "64", "hashOfConfig": "58"}, {"size": 6282, "mtime": 1751889768476, "results": "65", "hashOfConfig": "58"}, {"size": 6945, "mtime": 1751977927213, "results": "66", "hashOfConfig": "58"}, {"size": 10926, "mtime": 1751978905492, "results": "67", "hashOfConfig": "58"}, {"size": 18286, "mtime": 1751889768476, "results": "68", "hashOfConfig": "58"}, {"size": 8745, "mtime": 1751889768476, "results": "69", "hashOfConfig": "58"}, {"size": 10563, "mtime": 1751977927261, "results": "70", "hashOfConfig": "58"}, {"size": 24364, "mtime": 1751889768476, "results": "71", "hashOfConfig": "58"}, {"size": 7710, "mtime": 1751889768480, "results": "72", "hashOfConfig": "58"}, {"size": 3867, "mtime": 1751889768476, "results": "73", "hashOfConfig": "58"}, {"size": 1614, "mtime": 1751889768472, "results": "74", "hashOfConfig": "58"}, {"size": 4107, "mtime": 1751972373749, "results": "75", "hashOfConfig": "58"}, {"size": 540, "mtime": 1751973076868, "results": "76", "hashOfConfig": "58"}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3d95w", {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ypm7gh", {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js", ["188"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js", ["189"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js", ["190"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js", ["191"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js", ["192"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/App.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Login.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Navbar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Dashboard.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Topics.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroupDetail.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Producer.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/TopicDetail.js", ["193"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ClusterInfo.js", ["194"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Analytics.js", ["195"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/MessageBrowser.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Settings.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Sidebar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/socket.js", ["196"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/api.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/hooks/useDebounce.js", [], [], {"ruleId": "197", "severity": 1, "message": "198", "line": 23, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "201", "line": 23, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "202", "line": 40, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 40, "endColumn": 7}, {"ruleId": "203", "severity": 1, "message": "204", "line": 79, "column": 1, "nodeType": "205", "endLine": 79, "endColumn": 36}, {"ruleId": "197", "severity": 1, "message": "198", "line": 27, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 27, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "202", "line": 40, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 40, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "198", "line": 23, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "198", "line": 27, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 27, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "204", "line": 79, "column": 1, "nodeType": "205", "endLine": 79, "endColumn": 36}, "no-unused-vars", "'Warning' is defined but never used.", "Identifier", "unusedVar", "'History' is defined but never used.", "'Edit' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]