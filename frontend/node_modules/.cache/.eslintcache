[{"/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js": "1", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js": "2", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js": "3", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js": "5", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js": "6", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js": "7", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js": "8", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js": "9", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js": "10", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js": "11", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js": "12", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js": "13", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js": "14", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js": "15", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js": "16", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js": "17", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js": "18", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js": "19", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/App.js": "20", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/ProtectedRoute.js": "21", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Login.js": "22", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Navbar.js": "23", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Dashboard.js": "24", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Topics.js": "25", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroupDetail.js": "26", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js": "27", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Producer.js": "28", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/TopicDetail.js": "29", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ClusterInfo.js": "30", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Analytics.js": "31", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/MessageBrowser.js": "32", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Settings.js": "33", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Sidebar.js": "34", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/socket.js": "35", "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/api.js": "36"}, {"size": 3177, "mtime": 1751888199075, "results": "37", "hashOfConfig": "38"}, {"size": 6294, "mtime": 1751888799347, "results": "39", "hashOfConfig": "38"}, {"size": 6709, "mtime": 1751875530445, "results": "40", "hashOfConfig": "38"}, {"size": 8590, "mtime": 1751889099210, "results": "41", "hashOfConfig": "38"}, {"size": 6282, "mtime": 1751631936614, "results": "42", "hashOfConfig": "38"}, {"size": 14711, "mtime": 1751889035484, "results": "43", "hashOfConfig": "38"}, {"size": 8745, "mtime": 1751631930603, "results": "44", "hashOfConfig": "38"}, {"size": 24364, "mtime": 1751889065121, "results": "45", "hashOfConfig": "38"}, {"size": 9650, "mtime": 1751631928592, "results": "46", "hashOfConfig": "38"}, {"size": 7710, "mtime": 1751631934963, "results": "47", "hashOfConfig": "38"}, {"size": 18286, "mtime": 1751884544652, "results": "48", "hashOfConfig": "38"}, {"size": 7379, "mtime": 1751888900779, "results": "49", "hashOfConfig": "38"}, {"size": 3867, "mtime": 1751889014335, "results": "50", "hashOfConfig": "38"}, {"size": 4107, "mtime": 1751887209304, "results": "51", "hashOfConfig": "38"}, {"size": 1614, "mtime": 1751631890462, "results": "52", "hashOfConfig": "38"}, {"size": 7966, "mtime": 1751888244160, "results": "53", "hashOfConfig": "38"}, {"size": 384, "mtime": 1751876231921, "results": "54", "hashOfConfig": "38"}, {"size": 10563, "mtime": 1751882224072, "results": "55", "hashOfConfig": "38"}, {"size": 3177, "mtime": 1751972373749, "results": "56", "hashOfConfig": "57"}, {"size": 6294, "mtime": 1751889768472, "results": "58", "hashOfConfig": "57"}, {"size": 384, "mtime": 1751889768476, "results": "59", "hashOfConfig": "57"}, {"size": 7966, "mtime": 1751889768476, "results": "60", "hashOfConfig": "57"}, {"size": 7379, "mtime": 1751889768476, "results": "61", "hashOfConfig": "57"}, {"size": 8590, "mtime": 1751889768476, "results": "62", "hashOfConfig": "57"}, {"size": 14711, "mtime": 1751972375165, "results": "63", "hashOfConfig": "57"}, {"size": 6282, "mtime": 1751889768476, "results": "64", "hashOfConfig": "57"}, {"size": 6709, "mtime": 1751889768476, "results": "65", "hashOfConfig": "57"}, {"size": 9650, "mtime": 1751889768476, "results": "66", "hashOfConfig": "57"}, {"size": 18286, "mtime": 1751889768476, "results": "67", "hashOfConfig": "57"}, {"size": 8745, "mtime": 1751889768476, "results": "68", "hashOfConfig": "57"}, {"size": 10563, "mtime": 1751889768476, "results": "69", "hashOfConfig": "57"}, {"size": 24364, "mtime": 1751889768476, "results": "70", "hashOfConfig": "57"}, {"size": 7710, "mtime": 1751889768480, "results": "71", "hashOfConfig": "57"}, {"size": 3867, "mtime": 1751889768476, "results": "72", "hashOfConfig": "57"}, {"size": 1614, "mtime": 1751889768472, "results": "73", "hashOfConfig": "57"}, {"size": 4107, "mtime": 1751972373749, "results": "74", "hashOfConfig": "57"}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3d95w", {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ypm7gh", {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js", ["183"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js", ["184"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js", ["185"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js", ["186"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js", ["187"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/App.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Login.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Navbar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Dashboard.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Topics.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroupDetail.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Producer.js", ["188"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/TopicDetail.js", ["189"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ClusterInfo.js", ["190"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Analytics.js", ["191"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/MessageBrowser.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/Settings.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/components/Layout/Sidebar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/socket.js", ["192"], [], "/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/api.js", [], [], {"ruleId": "193", "severity": 1, "message": "194", "line": 23, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 23, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "197", "line": 23, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 23, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "198", "line": 40, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 40, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "200", "line": 79, "column": 1, "nodeType": "201", "endLine": 79, "endColumn": 36}, {"ruleId": "193", "severity": 1, "message": "194", "line": 27, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 27, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "197", "line": 23, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 23, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "198", "line": 40, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 40, "endColumn": 7}, {"ruleId": "193", "severity": 1, "message": "194", "line": 23, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 23, "endColumn": 10}, {"ruleId": "193", "severity": 1, "message": "194", "line": 27, "column": 3, "nodeType": "195", "messageId": "196", "endLine": 27, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "200", "line": 79, "column": 1, "nodeType": "201", "endLine": 79, "endColumn": 36}, "no-unused-vars", "'Warning' is defined but never used.", "Identifier", "unusedVar", "'History' is defined but never used.", "'Edit' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]