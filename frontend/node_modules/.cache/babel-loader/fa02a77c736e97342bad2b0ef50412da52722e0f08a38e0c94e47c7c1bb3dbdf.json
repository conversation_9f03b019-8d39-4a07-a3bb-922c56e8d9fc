{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\n\n/**\n * Custom hook for debouncing values\n * @param {any} value - The value to debounce\n * @param {number} delay - Delay in milliseconds\n * @returns {any} Debounced value\n */\nexport function useDebounce(value, delay) {\n  _s();\n  const [debouncedValue, setDebouncedValue] = useState(value);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n  return debouncedValue;\n}\n\n/**\n * Custom hook for debounced callback\n * @param {Function} callback - The callback function to debounce\n * @param {number} delay - Delay in milliseconds\n * @param {Array} deps - Dependencies array\n * @returns {Function} Debounced callback function\n */\n_s(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");\nexport function useDebouncedCallback(callback, delay, deps = []) {\n  _s2();\n  const [debounceTimer, setDebounceTimer] = useState(null);\n  const debouncedCallback = (...args) => {\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n    }\n    const newTimer = setTimeout(() => {\n      callback(...args);\n    }, delay);\n    setDebounceTimer(newTimer);\n  };\n  useEffect(() => {\n    return () => {\n      if (debounceTimer) {\n        clearTimeout(debounceTimer);\n      }\n    };\n  }, [debounceTimer]);\n\n  // Clean up on deps change\n  useEffect(() => {\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n      setDebounceTimer(null);\n    }\n  }, deps);\n  return debouncedCallback;\n}\n_s2(useDebouncedCallback, \"mKc1+YZHtuX240+BLwLpKX/sFEc=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useDebounce", "value", "delay", "_s", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "setTimeout", "clearTimeout", "useDebouncedCallback", "callback", "deps", "_s2", "deboun<PERSON><PERSON><PERSON>r", "setDebounceTimer", "deboun<PERSON><PERSON><PERSON><PERSON>", "args", "newTimer"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/hooks/useDebounce.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\n/**\n * Custom hook for debouncing values\n * @param {any} value - The value to debounce\n * @param {number} delay - Delay in milliseconds\n * @returns {any} Debounced value\n */\nexport function useDebounce(value, delay) {\n  const [debouncedValue, setDebouncedValue] = useState(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\n/**\n * Custom hook for debounced callback\n * @param {Function} callback - The callback function to debounce\n * @param {number} delay - Delay in milliseconds\n * @param {Array} deps - Dependencies array\n * @returns {Function} Debounced callback function\n */\nexport function useDebouncedCallback(callback, delay, deps = []) {\n  const [debounceTimer, setDebounceTimer] = useState(null);\n\n  const debouncedCallback = (...args) => {\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n    }\n\n    const newTimer = setTimeout(() => {\n      callback(...args);\n    }, delay);\n\n    setDebounceTimer(newTimer);\n  };\n\n  useEffect(() => {\n    return () => {\n      if (debounceTimer) {\n        clearTimeout(debounceTimer);\n      }\n    };\n  }, [debounceTimer]);\n\n  // Clean up on deps change\n  useEffect(() => {\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n      setDebounceTimer(null);\n    }\n  }, deps);\n\n  return debouncedCallback;\n}\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAAAC,EAAA;EACxC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGP,QAAQ,CAACG,KAAK,CAAC;EAE3DF,SAAS,CAAC,MAAM;IACd,MAAMO,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BF,iBAAiB,CAACJ,KAAK,CAAC;IAC1B,CAAC,EAAEC,KAAK,CAAC;IAET,OAAO,MAAM;MACXM,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEC,KAAK,CAAC,CAAC;EAElB,OAAOE,cAAc;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAD,EAAA,CAhBgBH,WAAW;AAuB3B,OAAO,SAASS,oBAAoBA,CAACC,QAAQ,EAAER,KAAK,EAAES,IAAI,GAAG,EAAE,EAAE;EAAAC,GAAA;EAC/D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMiB,iBAAiB,GAAGA,CAAC,GAAGC,IAAI,KAAK;IACrC,IAAIH,aAAa,EAAE;MACjBL,YAAY,CAACK,aAAa,CAAC;IAC7B;IAEA,MAAMI,QAAQ,GAAGV,UAAU,CAAC,MAAM;MAChCG,QAAQ,CAAC,GAAGM,IAAI,CAAC;IACnB,CAAC,EAAEd,KAAK,CAAC;IAETY,gBAAgB,CAACG,QAAQ,CAAC;EAC5B,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIc,aAAa,EAAE;QACjBL,YAAY,CAACK,aAAa,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACAd,SAAS,CAAC,MAAM;IACd,IAAIc,aAAa,EAAE;MACjBL,YAAY,CAACK,aAAa,CAAC;MAC3BC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,EAAEH,IAAI,CAAC;EAER,OAAOI,iBAAiB;AAC1B;AAACH,GAAA,CAhCeH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}