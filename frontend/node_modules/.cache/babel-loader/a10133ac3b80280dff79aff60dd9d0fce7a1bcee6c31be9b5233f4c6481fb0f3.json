{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\n\n// Create a responsive theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    }\n  },\n  typography: {\n    h4: {\n      fontSize: '2.125rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.5rem'\n      }\n    },\n    h5: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.25rem'\n      }\n    },\n    h6: {\n      fontSize: '1.25rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.125rem'\n      }\n    },\n    body1: {\n      fontSize: '1rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.875rem'\n      }\n    },\n    body2: {\n      fontSize: '0.875rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.75rem'\n      }\n    }\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }\n      }\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          fontWeight: 500\n        }\n      }\n    },\n    MuiTableCell: {\n      styleOverrides: {\n        root: {\n          padding: '12px 16px',\n          '@media (max-width:600px)': {\n            padding: '8px 12px'\n          }\n        }\n      }\n    }\n  },\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 960,\n      lg: 1280,\n      xl: 1920\n    }\n  }\n});\n\n// Create an optimized query client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: (failureCount, error) => {\n        var _error$response, _error$response2;\n        // Don't retry on 4xx errors (client errors)\n        if ((error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) >= 400 && (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) < 500) {\n          return false;\n        }\n        // Retry up to 2 times for other errors\n        return failureCount < 2;\n      },\n      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\n      // Exponential backoff\n      refetchOnWindowFocus: false,\n      refetchOnReconnect: true,\n      staleTime: 2 * 60 * 1000,\n      // 2 minutes - reduced for more frequent updates\n      cacheTime: 10 * 60 * 1000,\n      // 10 minutes - keep in cache longer\n      // Enable background refetching for better UX\n      refetchInterval: false,\n      // Disable automatic refetching by default\n      refetchIntervalInBackground: false,\n      // Optimize network usage\n      networkMode: 'online'\n    },\n    mutations: {\n      retry: 1,\n      networkMode: 'online'\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n      client: queryClient,\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n          position: \"top-right\",\n          toastOptions: {\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff'\n            },\n            success: {\n              duration: 3000,\n              iconTheme: {\n                primary: '#4caf50',\n                secondary: '#fff'\n              }\n            },\n            error: {\n              duration: 5000,\n              iconTheme: {\n                primary: '#f44336',\n                secondary: '#fff'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 133,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryClient", "QueryClientProvider", "ThemeProvider", "createTheme", "CssBaseline", "Toaster", "App", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "typography", "h4", "fontSize", "fontWeight", "h5", "h6", "body1", "body2", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "MuiCard", "borderRadius", "boxShadow", "MuiChip", "MuiTableCell", "padding", "breakpoints", "values", "xs", "sm", "md", "lg", "xl", "queryClient", "defaultOptions", "queries", "retry", "failureCount", "error", "_error$response", "_error$response2", "response", "status", "retry<PERSON><PERSON><PERSON>", "attemptIndex", "Math", "min", "refetchOnWindowFocus", "refetchOnReconnect", "staleTime", "cacheTime", "refetchInterval", "refetchIntervalInBackground", "networkMode", "mutations", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "client", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { <PERSON><PERSON>erRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\n\n// Create a responsive theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n  },\n  typography: {\n    h4: {\n      fontSize: '2.125rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.5rem',\n      },\n    },\n    h5: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.25rem',\n      },\n    },\n    h6: {\n      fontSize: '1.25rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.125rem',\n      },\n    },\n    body1: {\n      fontSize: '1rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.875rem',\n      },\n    },\n    body2: {\n      fontSize: '0.875rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.75rem',\n      },\n    },\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiTableCell: {\n      styleOverrides: {\n        root: {\n          padding: '12px 16px',\n          '@media (max-width:600px)': {\n            padding: '8px 12px',\n          },\n        },\n      },\n    },\n  },\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 960,\n      lg: 1280,\n      xl: 1920,\n    },\n  },\n});\n\n// Create an optimized query client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: (failureCount, error) => {\n        // Don't retry on 4xx errors (client errors)\n        if (error?.response?.status >= 400 && error?.response?.status < 500) {\n          return false;\n        }\n        // Retry up to 2 times for other errors\n        return failureCount < 2;\n      },\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff\n      refetchOnWindowFocus: false,\n      refetchOnReconnect: true,\n      staleTime: 2 * 60 * 1000, // 2 minutes - reduced for more frequent updates\n      cacheTime: 10 * 60 * 1000, // 10 minutes - keep in cache longer\n      // Enable background refetching for better UX\n      refetchInterval: false, // Disable automatic refetching by default\n      refetchIntervalInBackground: false,\n      // Optimize network usage\n      networkMode: 'online',\n    },\n    mutations: {\n      retry: 1,\n      networkMode: 'online',\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <QueryClientProvider client={queryClient}>\n        <BrowserRouter>\n          <App />\n          <Toaster\n            position=\"top-right\"\n            toastOptions={{\n              duration: 4000,\n              style: {\n                background: '#363636',\n                color: '#fff',\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#4caf50',\n                  secondary: '#fff',\n                },\n              },\n              error: {\n                duration: 5000,\n                iconTheme: {\n                  primary: '#f44336',\n                  secondary: '#fff',\n                },\n              },\n            }}\n          />\n        </BrowserRouter>\n      </QueryClientProvider>\n    </ThemeProvider>\n  </React.StrictMode>\n); "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGN,WAAW,CAAC;EACxBO,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR;EACF,CAAC;EACDE,UAAU,EAAE;IACVC,EAAE,EAAE;MACFC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,GAAG;MACf,0BAA0B,EAAE;QAC1BD,QAAQ,EAAE;MACZ;IACF,CAAC;IACDE,EAAE,EAAE;MACFF,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,GAAG;MACf,0BAA0B,EAAE;QAC1BD,QAAQ,EAAE;MACZ;IACF,CAAC;IACDG,EAAE,EAAE;MACFH,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,GAAG;MACf,0BAA0B,EAAE;QAC1BD,QAAQ,EAAE;MACZ;IACF,CAAC;IACDI,KAAK,EAAE;MACLJ,QAAQ,EAAE,MAAM;MAChB,0BAA0B,EAAE;QAC1BA,QAAQ,EAAE;MACZ;IACF,CAAC;IACDK,KAAK,EAAE;MACLL,QAAQ,EAAE,UAAU;MACpB,0BAA0B,EAAE;QAC1BA,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDM,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,aAAa,EAAE,MAAM;UACrBT,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDU,OAAO,EAAE;MACPH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;QACb;MACF;IACF,CAAC;IACDC,OAAO,EAAE;MACPN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJR,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDc,YAAY,EAAE;MACZP,cAAc,EAAE;QACdC,IAAI,EAAE;UACJO,OAAO,EAAE,WAAW;UACpB,0BAA0B,EAAE;YAC1BA,OAAO,EAAE;UACX;QACF;MACF;IACF;EACF,CAAC;EACDC,WAAW,EAAE;IACXC,MAAM,EAAE;MACNC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,IAAI;MACRC,EAAE,EAAE;IACN;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,GAAG,IAAIxC,WAAW,CAAC;EAClCyC,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAEA,CAACC,YAAY,EAAEC,KAAK,KAAK;QAAA,IAAAC,eAAA,EAAAC,gBAAA;QAC9B;QACA,IAAI,CAAAF,KAAK,aAALA,KAAK,wBAAAC,eAAA,GAALD,KAAK,CAAEG,QAAQ,cAAAF,eAAA,uBAAfA,eAAA,CAAiBG,MAAM,KAAI,GAAG,IAAI,CAAAJ,KAAK,aAALA,KAAK,wBAAAE,gBAAA,GAALF,KAAK,CAAEG,QAAQ,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBE,MAAM,IAAG,GAAG,EAAE;UACnE,OAAO,KAAK;QACd;QACA;QACA,OAAOL,YAAY,GAAG,CAAC;MACzB,CAAC;MACDM,UAAU,EAAGC,YAAY,IAAKC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIF,YAAY,EAAE,KAAK,CAAC;MAAE;MACzEG,oBAAoB,EAAE,KAAK;MAC3BC,kBAAkB,EAAE,IAAI;MACxBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MAC3B;MACAC,eAAe,EAAE,KAAK;MAAE;MACxBC,2BAA2B,EAAE,KAAK;MAClC;MACAC,WAAW,EAAE;IACf,CAAC;IACDC,SAAS,EAAE;MACTlB,KAAK,EAAE,CAAC;MACRiB,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;AAEF,MAAMnC,IAAI,GAAG3B,QAAQ,CAACgE,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEvC,IAAI,CAACwC,MAAM,cACTzD,OAAA,CAACX,KAAK,CAACqE,UAAU;EAAAC,QAAA,eACf3D,OAAA,CAACN,aAAa;IAACO,KAAK,EAAEA,KAAM;IAAA0D,QAAA,gBAC1B3D,OAAA,CAACJ,WAAW;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf/D,OAAA,CAACP,mBAAmB;MAACuE,MAAM,EAAEhC,WAAY;MAAA2B,QAAA,eACvC3D,OAAA,CAACT,aAAa;QAAAoE,QAAA,gBACZ3D,OAAA,CAACF,GAAG;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACP/D,OAAA,CAACH,OAAO;UACNoE,QAAQ,EAAC,WAAW;UACpBC,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;cACLC,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE;YACT,CAAC;YACDC,OAAO,EAAE;cACPJ,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTrE,OAAO,EAAE,SAAS;gBAClBE,SAAS,EAAE;cACb;YACF,CAAC;YACDgC,KAAK,EAAE;cACL8B,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTrE,OAAO,EAAE,SAAS;gBAClBE,SAAS,EAAE;cACb;YACF;UACF;QAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}