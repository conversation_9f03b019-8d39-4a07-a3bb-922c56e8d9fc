{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, Chip, Button, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Alert, CircularProgress, TextField, InputAdornment } from '@mui/material';\nimport { GroupWork, Delete, Visibility, Refresh, Search, Clear } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\nimport { consumerGroupsApi } from '../services/api';\nimport { useDebounce } from '../hooks/useDebounce';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConsumerGroupCard = ({\n  group,\n  onView,\n  onDelete\n}) => {\n  var _group$members;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(GroupWork, {\n          sx: {\n            mr: 1,\n            color: 'primary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"h2\",\n          children: group.groupId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          mb: 2,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `State: ${group.state}`,\n          size: \"small\",\n          color: group.state === 'Stable' ? 'success' : 'warning',\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Members: ${((_group$members = group.members) === null || _group$members === void 0 ? void 0 : _group$members.length) || 0}`,\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Protocol: ${group.protocolType}`,\n          size: \"small\",\n          color: \"secondary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          mt: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          startIcon: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 22\n          }, this),\n          onClick: () => onView(group.groupId),\n          children: \"View Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => onDelete(group.groupId),\n          color: \"error\",\n          children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 3\n  }, this);\n};\n_c = ConsumerGroupCard;\nconst ConsumerGroups = () => {\n  _s();\n  var _consumerGroups$data;\n  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);\n  const [groupToDelete, setGroupToDelete] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n  const {\n    data: consumerGroups,\n    isLoading\n  } = useQuery('consumer-groups', consumerGroupsApi.getAll, {\n    refetchInterval: 30000\n  });\n\n  // Filter consumer groups based on search term\n  const filteredGroups = useMemo(() => {\n    if (!(consumerGroups !== null && consumerGroups !== void 0 && consumerGroups.data) || !searchTerm.trim()) {\n      return (consumerGroups === null || consumerGroups === void 0 ? void 0 : consumerGroups.data) || [];\n    }\n    return consumerGroups.data.filter(group => group.groupId.toLowerCase().includes(searchTerm.toLowerCase()));\n  }, [consumerGroups === null || consumerGroups === void 0 ? void 0 : consumerGroups.data, searchTerm]);\n  const deleteMutation = useMutation(consumerGroupsApi.delete, {\n    onSuccess: () => {\n      toast.success('Consumer group deleted successfully');\n      queryClient.invalidateQueries('consumer-groups');\n      setDeleteConfirmOpen(false);\n      setGroupToDelete(null);\n    },\n    onError: error => {\n      toast.error(`Error deleting consumer group: ${error.message}`);\n    }\n  });\n  const handleDeleteGroup = groupId => {\n    setGroupToDelete(groupId);\n    setDeleteConfirmOpen(true);\n  };\n  const confirmDelete = () => {\n    if (groupToDelete) {\n      deleteMutation.mutate(groupToDelete);\n    }\n  };\n  const handleViewGroup = groupId => {\n    navigate(`/consumer-groups/${groupId}`);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: \"Consumer Groups\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 22\n        }, this),\n        onClick: () => queryClient.invalidateQueries('consumer-groups'),\n        children: \"Refresh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        placeholder: \"Search consumer groups...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"start\",\n            children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this),\n          endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"clear search\",\n              onClick: () => setSearchTerm(''),\n              edge: \"end\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(Clear, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)\n        },\n        variant: \"outlined\",\n        size: \"medium\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), searchTerm && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: [filteredGroups.length, \" consumer group(s) found for \\\"\", searchTerm, \"\\\"\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this), (consumerGroups === null || consumerGroups === void 0 ? void 0 : (_consumerGroups$data = consumerGroups.data) === null || _consumerGroups$data === void 0 ? void 0 : _consumerGroups$data.length) === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: \"No consumer groups found.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this) : filteredGroups.length === 0 && searchTerm ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"No consumer groups found matching \\\"\", searchTerm, \"\\\". Try a different search term.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: filteredGroups.map(group => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ConsumerGroupCard, {\n          group: group,\n          onView: handleViewGroup,\n          onDelete: handleDeleteGroup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 15\n        }, this)\n      }, group.groupId, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteConfirmOpen,\n      onClose: () => setDeleteConfirmOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete consumer group \\\"\", groupToDelete, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteConfirmOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: confirmDelete,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(ConsumerGroups, \"xDcQ74uKg5o5UdFowpwqLnqnbpY=\", false, function () {\n  return [useNavigate, useQueryClient, useQuery, useMutation];\n});\n_c2 = ConsumerGroups;\nexport default ConsumerGroups;\nvar _c, _c2;\n$RefreshReg$(_c, \"ConsumerGroupCard\");\n$RefreshReg$(_c2, \"ConsumerGroups\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Chip", "<PERSON><PERSON>", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "CircularProgress", "TextField", "InputAdornment", "GroupWork", "Delete", "Visibility", "Refresh", "Search", "Clear", "useQuery", "useMutation", "useQueryClient", "useNavigate", "toast", "consumerGroupsApi", "useDebounce", "jsxDEV", "_jsxDEV", "ConsumerGroupCard", "group", "onView", "onDelete", "_group$members", "sx", "height", "display", "flexDirection", "children", "flexGrow", "alignItems", "mb", "mr", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "groupId", "gap", "flexWrap", "label", "state", "size", "members", "length", "protocolType", "mt", "startIcon", "onClick", "_c", "ConsumerGroups", "_s", "_consumerGroups$data", "deleteConfirmOpen", "setDeleteConfirmOpen", "groupToDelete", "setGroupToDelete", "searchTerm", "setSearchTerm", "navigate", "queryClient", "data", "consumerGroups", "isLoading", "getAll", "refetchInterval", "filteredGroups", "trim", "filter", "toLowerCase", "includes", "deleteMutation", "delete", "onSuccess", "success", "invalidateQueries", "onError", "error", "message", "handleDeleteGroup", "confirmDelete", "mutate", "handleViewGroup", "justifyContent", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "edge", "severity", "container", "spacing", "map", "item", "xs", "sm", "md", "open", "onClose", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/pages/ConsumerGroups.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  Chip,\n  Button,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  CircularProgress,\n  TextField,\n  InputAdornment,\n} from '@mui/material';\nimport {\n  GroupWork,\n  Delete,\n  Visibility,\n  Refresh,\n  Search,\n  Clear,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\nimport { consumerGroupsApi } from '../services/api';\nimport { useDebounce } from '../hooks/useDebounce';\n\nconst ConsumerGroupCard = ({ group, onView, onDelete }) => (\n  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n    <CardContent sx={{ flexGrow: 1 }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n        <GroupWork sx={{ mr: 1, color: 'primary.main' }} />\n        <Typography variant=\"h6\" component=\"h2\">\n          {group.groupId}\n        </Typography>\n      </Box>\n      \n      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>\n        <Chip\n          label={`State: ${group.state}`}\n          size=\"small\"\n          color={group.state === 'Stable' ? 'success' : 'warning'}\n          variant=\"outlined\"\n        />\n        <Chip\n          label={`Members: ${group.members?.length || 0}`}\n          size=\"small\"\n          color=\"primary\"\n          variant=\"outlined\"\n        />\n        <Chip\n          label={`Protocol: ${group.protocolType}`}\n          size=\"small\"\n          color=\"secondary\"\n          variant=\"outlined\"\n        />\n      </Box>\n\n      <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>\n        <Button\n          size=\"small\"\n          startIcon={<Visibility />}\n          onClick={() => onView(group.groupId)}\n        >\n          View Details\n        </Button>\n        <IconButton\n          size=\"small\"\n          onClick={() => onDelete(group.groupId)}\n          color=\"error\"\n        >\n          <Delete />\n        </IconButton>\n      </Box>\n    </CardContent>\n  </Card>\n);\n\nconst ConsumerGroups = () => {\n  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);\n  const [groupToDelete, setGroupToDelete] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  const { data: consumerGroups, isLoading } = useQuery(\n    'consumer-groups',\n    consumerGroupsApi.getAll,\n    {\n      refetchInterval: 30000,\n    }\n  );\n\n  // Filter consumer groups based on search term\n  const filteredGroups = useMemo(() => {\n    if (!consumerGroups?.data || !searchTerm.trim()) {\n      return consumerGroups?.data || [];\n    }\n    \n    return consumerGroups.data.filter(group => \n      group.groupId.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  }, [consumerGroups?.data, searchTerm]);\n\n  const deleteMutation = useMutation(consumerGroupsApi.delete, {\n    onSuccess: () => {\n      toast.success('Consumer group deleted successfully');\n      queryClient.invalidateQueries('consumer-groups');\n      setDeleteConfirmOpen(false);\n      setGroupToDelete(null);\n    },\n    onError: (error) => {\n      toast.error(`Error deleting consumer group: ${error.message}`);\n    },\n  });\n\n  const handleDeleteGroup = (groupId) => {\n    setGroupToDelete(groupId);\n    setDeleteConfirmOpen(true);\n  };\n\n  const confirmDelete = () => {\n    if (groupToDelete) {\n      deleteMutation.mutate(groupToDelete);\n    }\n  };\n\n  const handleViewGroup = (groupId) => {\n    navigate(`/consumer-groups/${groupId}`);\n  };\n\n  if (isLoading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>\n        <Typography variant=\"h4\">Consumer Groups</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<Refresh />}\n          onClick={() => queryClient.invalidateQueries('consumer-groups')}\n        >\n          Refresh\n        </Button>\n      </Box>\n\n      {/* Search Bar */}\n      <Box sx={{ mb: 3 }}>\n        <TextField\n          fullWidth\n          placeholder=\"Search consumer groups...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Search />\n              </InputAdornment>\n            ),\n            endAdornment: searchTerm && (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  aria-label=\"clear search\"\n                  onClick={() => setSearchTerm('')}\n                  edge=\"end\"\n                  size=\"small\"\n                >\n                  <Clear />\n                </IconButton>\n              </InputAdornment>\n            ),\n          }}\n          variant=\"outlined\"\n          size=\"medium\"\n        />\n      </Box>\n\n      {/* Results Info */}\n      {searchTerm && (\n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {filteredGroups.length} consumer group(s) found for \"{searchTerm}\"\n          </Typography>\n        </Box>\n      )}\n\n      {consumerGroups?.data?.length === 0 ? (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          No consumer groups found.\n        </Alert>\n      ) : filteredGroups.length === 0 && searchTerm ? (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          No consumer groups found matching \"{searchTerm}\". Try a different search term.\n        </Alert>\n      ) : (\n        <Grid container spacing={3}>\n          {filteredGroups.map((group) => (\n            <Grid item xs={12} sm={6} md={4} key={group.groupId}>\n              <ConsumerGroupCard\n                group={group}\n                onView={handleViewGroup}\n                onDelete={handleDeleteGroup}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      <Dialog\n        open={deleteConfirmOpen}\n        onClose={() => setDeleteConfirmOpen(false)}\n      >\n        <DialogTitle>Confirm Delete</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete consumer group \"{groupToDelete}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>\n          <Button onClick={confirmDelete} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ConsumerGroups; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACTC,cAAc,QACT,eAAe;AACtB,SACEC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,WAAW,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC;EAAA,IAAAC,cAAA;EAAA,oBACpDL,OAAA,CAAC5B,IAAI;IAACkC,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,eACrEV,OAAA,CAAC3B,WAAW;MAACiC,EAAE,EAAE;QAAEK,QAAQ,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAC/BV,OAAA,CAAC9B,GAAG;QAACoC,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEI,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACxDV,OAAA,CAACd,SAAS;UAACoB,EAAE,EAAE;YAAEQ,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDnB,OAAA,CAAC7B,UAAU;UAACiD,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAX,QAAA,EACpCR,KAAK,CAACoB;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENnB,OAAA,CAAC9B,GAAG;QAACoC,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE,CAAC;UAAEV,EAAE,EAAE,CAAC;UAAEW,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,gBAC5DV,OAAA,CAACzB,IAAI;UACHkD,KAAK,EAAE,UAAUvB,KAAK,CAACwB,KAAK,EAAG;UAC/BC,IAAI,EAAC,OAAO;UACZZ,KAAK,EAAEb,KAAK,CAACwB,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;UACxDN,OAAO,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFnB,OAAA,CAACzB,IAAI;UACHkD,KAAK,EAAE,YAAY,EAAApB,cAAA,GAAAH,KAAK,CAAC0B,OAAO,cAAAvB,cAAA,uBAAbA,cAAA,CAAewB,MAAM,KAAI,CAAC,EAAG;UAChDF,IAAI,EAAC,OAAO;UACZZ,KAAK,EAAC,SAAS;UACfK,OAAO,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFnB,OAAA,CAACzB,IAAI;UACHkD,KAAK,EAAE,aAAavB,KAAK,CAAC4B,YAAY,EAAG;UACzCH,IAAI,EAAC,OAAO;UACZZ,KAAK,EAAC,WAAW;UACjBK,OAAO,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnB,OAAA,CAAC9B,GAAG;QAACoC,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE,CAAC;UAAEQ,EAAE,EAAE;QAAO,CAAE;QAAArB,QAAA,gBAC/CV,OAAA,CAACxB,MAAM;UACLmD,IAAI,EAAC,OAAO;UACZK,SAAS,eAAEhC,OAAA,CAACZ,UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Bc,OAAO,EAAEA,CAAA,KAAM9B,MAAM,CAACD,KAAK,CAACoB,OAAO,CAAE;UAAAZ,QAAA,EACtC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA,CAACvB,UAAU;UACTkD,IAAI,EAAC,OAAO;UACZM,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAACF,KAAK,CAACoB,OAAO,CAAE;UACvCP,KAAK,EAAC,OAAO;UAAAL,QAAA,eAEbV,OAAA,CAACb,MAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAAA,CACR;AAACe,EAAA,GAjDIjC,iBAAiB;AAmDvB,MAAMkC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC3B,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM4E,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,WAAW,GAAGnD,cAAc,CAAC,CAAC;EAEpC,MAAM;IAAEoD,IAAI,EAAEC,cAAc;IAAEC;EAAU,CAAC,GAAGxD,QAAQ,CAClD,iBAAiB,EACjBK,iBAAiB,CAACoD,MAAM,EACxB;IACEC,eAAe,EAAE;EACnB,CACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGlF,OAAO,CAAC,MAAM;IACnC,IAAI,EAAC8E,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAED,IAAI,KAAI,CAACJ,UAAU,CAACU,IAAI,CAAC,CAAC,EAAE;MAC/C,OAAO,CAAAL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAED,IAAI,KAAI,EAAE;IACnC;IAEA,OAAOC,cAAc,CAACD,IAAI,CAACO,MAAM,CAACnD,KAAK,IACrCA,KAAK,CAACoB,OAAO,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,UAAU,CAACY,WAAW,CAAC,CAAC,CAC/D,CAAC;EACH,CAAC,EAAE,CAACP,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAED,IAAI,EAAEJ,UAAU,CAAC,CAAC;EAEtC,MAAMc,cAAc,GAAG/D,WAAW,CAACI,iBAAiB,CAAC4D,MAAM,EAAE;IAC3DC,SAAS,EAAEA,CAAA,KAAM;MACf9D,KAAK,CAAC+D,OAAO,CAAC,qCAAqC,CAAC;MACpDd,WAAW,CAACe,iBAAiB,CAAC,iBAAiB,CAAC;MAChDrB,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;IACDoB,OAAO,EAAGC,KAAK,IAAK;MAClBlE,KAAK,CAACkE,KAAK,CAAC,kCAAkCA,KAAK,CAACC,OAAO,EAAE,CAAC;IAChE;EACF,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAI1C,OAAO,IAAK;IACrCmB,gBAAgB,CAACnB,OAAO,CAAC;IACzBiB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzB,aAAa,EAAE;MACjBgB,cAAc,CAACU,MAAM,CAAC1B,aAAa,CAAC;IACtC;EACF,CAAC;EAED,MAAM2B,eAAe,GAAI7C,OAAO,IAAK;IACnCsB,QAAQ,CAAC,oBAAoBtB,OAAO,EAAE,CAAC;EACzC,CAAC;EAED,IAAI0B,SAAS,EAAE;IACb,oBACEhD,OAAA,CAAC9B,GAAG;MAACoC,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAE4D,cAAc,EAAE,QAAQ;QAAErC,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,eAC5DV,OAAA,CAACjB,gBAAgB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEnB,OAAA,CAAC9B,GAAG;IAACoC,EAAE,EAAE;MAAEK,QAAQ,EAAE;IAAE,CAAE;IAAAD,QAAA,gBACvBV,OAAA,CAAC9B,GAAG;MAACoC,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAE4D,cAAc,EAAE,eAAe;QAAExD,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzFV,OAAA,CAAC7B,UAAU;QAACiD,OAAO,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAe;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACrDnB,OAAA,CAACxB,MAAM;QACL4C,OAAO,EAAC,UAAU;QAClBY,SAAS,eAAEhC,OAAA,CAACX,OAAO;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBc,OAAO,EAAEA,CAAA,KAAMY,WAAW,CAACe,iBAAiB,CAAC,iBAAiB,CAAE;QAAAlD,QAAA,EACjE;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnB,OAAA,CAAC9B,GAAG;MAACoC,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACjBV,OAAA,CAAChB,SAAS;QACRqF,SAAS;QACTC,WAAW,EAAC,2BAA2B;QACvCC,KAAK,EAAE7B,UAAW;QAClB8B,QAAQ,EAAGC,CAAC,IAAK9B,aAAa,CAAC8B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CI,UAAU,EAAE;UACVC,cAAc,eACZ5E,OAAA,CAACf,cAAc;YAAC4F,QAAQ,EAAC,OAAO;YAAAnE,QAAA,eAC9BV,OAAA,CAACV,MAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACjB;UACD2D,YAAY,EAAEpC,UAAU,iBACtB1C,OAAA,CAACf,cAAc;YAAC4F,QAAQ,EAAC,KAAK;YAAAnE,QAAA,eAC5BV,OAAA,CAACvB,UAAU;cACT,cAAW,cAAc;cACzBwD,OAAO,EAAEA,CAAA,KAAMU,aAAa,CAAC,EAAE,CAAE;cACjCoC,IAAI,EAAC,KAAK;cACVpD,IAAI,EAAC,OAAO;cAAAjB,QAAA,eAEZV,OAAA,CAACT,KAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAEpB,CAAE;QACFC,OAAO,EAAC,UAAU;QAClBO,IAAI,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLuB,UAAU,iBACT1C,OAAA,CAAC9B,GAAG;MAACoC,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACjBV,OAAA,CAAC7B,UAAU;QAACiD,OAAO,EAAC,OAAO;QAACL,KAAK,EAAC,gBAAgB;QAAAL,QAAA,GAC/CyC,cAAc,CAACtB,MAAM,EAAC,iCAA8B,EAACa,UAAU,EAAC,IACnE;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEA,CAAA4B,cAAc,aAAdA,cAAc,wBAAAV,oBAAA,GAAdU,cAAc,CAAED,IAAI,cAAAT,oBAAA,uBAApBA,oBAAA,CAAsBR,MAAM,MAAK,CAAC,gBACjC7B,OAAA,CAAClB,KAAK;MAACkG,QAAQ,EAAC,MAAM;MAAC1E,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAEtC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,GACNgC,cAAc,CAACtB,MAAM,KAAK,CAAC,IAAIa,UAAU,gBAC3C1C,OAAA,CAAClB,KAAK;MAACkG,QAAQ,EAAC,MAAM;MAAC1E,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,GAAC,sCACD,EAACgC,UAAU,EAAC,kCACjD;IAAA;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAERnB,OAAA,CAAC1B,IAAI;MAAC2G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxE,QAAA,EACxByC,cAAc,CAACgC,GAAG,CAAEjF,KAAK,iBACxBF,OAAA,CAAC1B,IAAI;QAAC8G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7E,QAAA,eAC9BV,OAAA,CAACC,iBAAiB;UAChBC,KAAK,EAAEA,KAAM;UACbC,MAAM,EAAEgE,eAAgB;UACxB/D,QAAQ,EAAE4D;QAAkB;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC,GALkCjB,KAAK,CAACoB,OAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAM7C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAEDnB,OAAA,CAACtB,MAAM;MACL8G,IAAI,EAAElD,iBAAkB;MACxBmD,OAAO,EAAEA,CAAA,KAAMlD,oBAAoB,CAAC,KAAK,CAAE;MAAA7B,QAAA,gBAE3CV,OAAA,CAACrB,WAAW;QAAA+B,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzCnB,OAAA,CAACpB,aAAa;QAAA8B,QAAA,eACZV,OAAA,CAAC7B,UAAU;UAAAuC,QAAA,GAAC,mDACsC,EAAC8B,aAAa,EAAC,mCACjE;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBnB,OAAA,CAACnB,aAAa;QAAA6B,QAAA,gBACZV,OAAA,CAACxB,MAAM;UAACyD,OAAO,EAAEA,CAAA,KAAMM,oBAAoB,CAAC,KAAK,CAAE;UAAA7B,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnEnB,OAAA,CAACxB,MAAM;UAACyD,OAAO,EAAEgC,aAAc;UAAClD,KAAK,EAAC,OAAO;UAACK,OAAO,EAAC,WAAW;UAAAV,QAAA,EAAC;QAElE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACiB,EAAA,CA5JID,cAAc;EAAA,QAKDxC,WAAW,EACRD,cAAc,EAEUF,QAAQ,EAmB7BC,WAAW;AAAA;AAAAiG,GAAA,GA3B9BvD,cAAc;AA8JpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}