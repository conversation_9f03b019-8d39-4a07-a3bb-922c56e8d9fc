{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\n\n/**\n * Custom hook for debouncing values\n * @param {any} value - The value to debounce\n * @param {number} delay - Delay in milliseconds\n * @returns {any} Debounced value\n */\nexport function useDebounce(value, delay) {\n  _s();\n  const [debouncedValue, setDebouncedValue] = useState(value);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n  return debouncedValue;\n}\n_s(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useDebounce", "value", "delay", "_s", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "setTimeout", "clearTimeout"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/hooks/useDebounce.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\n/**\n * Custom hook for debouncing values\n * @param {any} value - The value to debounce\n * @param {number} delay - Delay in milliseconds\n * @returns {any} Debounced value\n */\nexport function useDebounce(value, delay) {\n  const [debouncedValue, setDebouncedValue] = useState(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAAAC,EAAA;EACxC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGP,QAAQ,CAACG,KAAK,CAAC;EAE3DF,SAAS,CAAC,MAAM;IACd,MAAMO,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BF,iBAAiB,CAACJ,KAAK,CAAC;IAC1B,CAAC,EAAEC,KAAK,CAAC;IAET,OAAO,MAAM;MACXM,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEC,KAAK,CAAC,CAAC;EAElB,OAAOE,cAAc;AACvB;AAACD,EAAA,CAdeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}