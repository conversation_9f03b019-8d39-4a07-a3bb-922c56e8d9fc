{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21 5h-2.64l1.14-3.14L17.15 1l-1.46 4H3v2l2 6-2 6v2h18v-2l-2-6 2-6zm-3.9 8.63L18.89 19H5.11l1.79-5.37.21-.63-.21-.63L5.11 7h13.78l-1.79 5.37-.21.63zM13 9h-2v3H8v2h3v3h2v-3h3v-2h-3z\"\n}), 'LocalPharmacyOutlined');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/node_modules/@mui/icons-material/esm/LocalPharmacyOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M21 5h-2.64l1.14-3.14L17.15 1l-1.46 4H3v2l2 6-2 6v2h18v-2l-2-6 2-6zm-3.9 8.63L18.89 19H5.11l1.79-5.37.21-.63-.21-.63L5.11 7h13.78l-1.79 5.37-.21.63zM13 9h-2v3H8v2h3v3h2v-3h3v-2h-3z\"\n}), 'LocalPharmacyOutlined');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}