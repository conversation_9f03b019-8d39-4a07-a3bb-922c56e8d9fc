{"ast": null, "code": "export { default } from './clamp';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/node_modules/@mui/utils/esm/clamp/index.js"], "sourcesContent": ["export { default } from './clamp';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}