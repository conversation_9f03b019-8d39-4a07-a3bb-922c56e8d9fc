{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useQuery, useQueryClient } from 'react-query';\nimport { topicsApi } from '../services/api';\n\n/**\n * Hook for progressive loading of topic data\n * First loads basic topic info, then loads message counts on-demand\n */\nexport const useProgressiveTopicData = (page, limit, searchTerm) => {\n  _s();\n  var _basicTopicsQuery$dat3, _basicTopicsQuery$dat4, _basicTopicsQuery$dat5, _basicTopicsQuery$dat6;\n  const [loadMessageCounts, setLoadMessageCounts] = useState(false);\n  const [visibleTopics, setVisibleTopics] = useState(new Set());\n  const queryClient = useQueryClient();\n\n  // Load basic topic data first (fast)\n  const basicTopicsQuery = useQuery(['topics-basic', page, limit, searchTerm], () => topicsApi.getPaginated({\n    page,\n    limit,\n    search: searchTerm,\n    includeCounts: false\n  }), {\n    refetchInterval: 30000,\n    keepPreviousData: true,\n    staleTime: 60000\n  });\n\n  // Load message counts separately (slower)\n  const messageCountsQuery = useQuery(['topics-with-counts', page, limit, searchTerm], () => topicsApi.getPaginated({\n    page,\n    limit,\n    search: searchTerm,\n    includeCounts: true\n  }), {\n    enabled: loadMessageCounts,\n    refetchInterval: 60000,\n    // Less frequent updates for counts\n    keepPreviousData: true,\n    staleTime: 120000 // 2 minutes\n  });\n\n  // Merge basic data with message counts when available\n  const mergedTopics = useCallback(() => {\n    var _basicTopicsQuery$dat, _basicTopicsQuery$dat2, _messageCountsQuery$d, _messageCountsQuery$d2;\n    const basicTopics = ((_basicTopicsQuery$dat = basicTopicsQuery.data) === null || _basicTopicsQuery$dat === void 0 ? void 0 : (_basicTopicsQuery$dat2 = _basicTopicsQuery$dat.data) === null || _basicTopicsQuery$dat2 === void 0 ? void 0 : _basicTopicsQuery$dat2.data) || [];\n    const topicsWithCounts = ((_messageCountsQuery$d = messageCountsQuery.data) === null || _messageCountsQuery$d === void 0 ? void 0 : (_messageCountsQuery$d2 = _messageCountsQuery$d.data) === null || _messageCountsQuery$d2 === void 0 ? void 0 : _messageCountsQuery$d2.data) || [];\n    if (topicsWithCounts.length > 0) {\n      // Create a map for quick lookup\n      const countsMap = new Map(topicsWithCounts.map(topic => [topic.name, topic]));\n      return basicTopics.map(topic => {\n        var _countsMap$get, _countsMap$get2;\n        return {\n          ...topic,\n          totalMessages: (_countsMap$get = countsMap.get(topic.name)) === null || _countsMap$get === void 0 ? void 0 : _countsMap$get.totalMessages,\n          partitionDetails: ((_countsMap$get2 = countsMap.get(topic.name)) === null || _countsMap$get2 === void 0 ? void 0 : _countsMap$get2.partitionDetails) || topic.partitionDetails\n        };\n      });\n    }\n    return basicTopics;\n  }, [basicTopicsQuery.data, messageCountsQuery.data]);\n\n  // Auto-load message counts after basic data is loaded and user has been on page for a bit\n  useEffect(() => {\n    if (basicTopicsQuery.data && !loadMessageCounts) {\n      const timer = setTimeout(() => {\n        setLoadMessageCounts(true);\n      }, 1000); // Load counts after 1 second\n\n      return () => clearTimeout(timer);\n    }\n  }, [basicTopicsQuery.data, loadMessageCounts]);\n\n  // Track which topics are visible for lazy loading\n  const markTopicVisible = useCallback(topicName => {\n    setVisibleTopics(prev => new Set([...prev, topicName]));\n  }, []);\n\n  // Force load message counts immediately\n  const forceLoadMessageCounts = useCallback(() => {\n    setLoadMessageCounts(true);\n  }, []);\n\n  // Refresh all data\n  const refreshData = useCallback(() => {\n    queryClient.invalidateQueries(['topics-basic']);\n    queryClient.invalidateQueries(['topics-with-counts']);\n  }, [queryClient]);\n  return {\n    // Data\n    topics: mergedTopics(),\n    pagination: ((_basicTopicsQuery$dat3 = basicTopicsQuery.data) === null || _basicTopicsQuery$dat3 === void 0 ? void 0 : (_basicTopicsQuery$dat4 = _basicTopicsQuery$dat3.data) === null || _basicTopicsQuery$dat4 === void 0 ? void 0 : _basicTopicsQuery$dat4.pagination) || {},\n    metadata: {\n      ...((_basicTopicsQuery$dat5 = basicTopicsQuery.data) === null || _basicTopicsQuery$dat5 === void 0 ? void 0 : (_basicTopicsQuery$dat6 = _basicTopicsQuery$dat5.data) === null || _basicTopicsQuery$dat6 === void 0 ? void 0 : _basicTopicsQuery$dat6.metadata),\n      messageCountsLoaded: messageCountsQuery.data !== undefined,\n      messageCountsLoading: messageCountsQuery.isLoading\n    },\n    // Loading states\n    isLoading: basicTopicsQuery.isLoading,\n    isLoadingCounts: messageCountsQuery.isLoading,\n    error: basicTopicsQuery.error || messageCountsQuery.error,\n    // Actions\n    markTopicVisible,\n    forceLoadMessageCounts,\n    refreshData,\n    // State\n    messageCountsEnabled: loadMessageCounts,\n    visibleTopics\n  };\n};\n\n/**\n * Hook for loading individual topic message count on-demand\n */\n_s(useProgressiveTopicData, \"hdDG7eFEMuUruRyP1++f49DggHE=\", false, function () {\n  return [useQueryClient, useQuery, useQuery];\n});\nexport const useTopicMessageCount = (topicName, enabled = false) => {\n  _s2();\n  return useQuery(['topic-message-count', topicName], async () => {\n    const response = await topicsApi.getMessageCounts([topicName]);\n    return response.data.data[0]; // Returns the first (and only) topic data\n  }, {\n    enabled: enabled && !!topicName,\n    staleTime: 120000,\n    // 2 minutes\n    refetchInterval: false // Don't auto-refetch\n  });\n};\n\n/**\n * Hook for batch loading message counts for multiple topics\n */\n_s2(useTopicMessageCount, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useBatchMessageCounts = (topicNames, enabled = false) => {\n  _s3();\n  return useQuery(['batch-message-counts', topicNames.sort().join(',')], async () => {\n    if (topicNames.length === 0) return [];\n    const response = await topicsApi.getMessageCounts(topicNames);\n    return response.data.data;\n  }, {\n    enabled: enabled && topicNames.length > 0,\n    staleTime: 120000,\n    // 2 minutes\n    refetchInterval: false\n  });\n};\n_s3(useBatchMessageCounts, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useQuery", "useQueryClient", "topicsApi", "useProgressiveTopicData", "page", "limit", "searchTerm", "_s", "_basicTopicsQuery$dat3", "_basicTopicsQuery$dat4", "_basicTopicsQuery$dat5", "_basicTopicsQuery$dat6", "loadMessageCounts", "setLoadMessageCounts", "visibleTopics", "setVisibleTopics", "Set", "queryClient", "basicTopicsQuery", "getPaginated", "search", "includeCounts", "refetchInterval", "keepPreviousData", "staleTime", "messageCountsQuery", "enabled", "mergedTopics", "_basicTopicsQuery$dat", "_basicTopicsQuery$dat2", "_messageCountsQuery$d", "_messageCountsQuery$d2", "basicTopics", "data", "topicsWithCounts", "length", "countsMap", "Map", "map", "topic", "name", "_countsMap$get", "_countsMap$get2", "totalMessages", "get", "partitionDetails", "timer", "setTimeout", "clearTimeout", "markTopicVisible", "topicName", "prev", "forceLoadMessageCounts", "refreshData", "invalidateQueries", "topics", "pagination", "metadata", "messageCountsLoaded", "undefined", "messageCountsLoading", "isLoading", "isLoadingCounts", "error", "messageCountsEnabled", "useTopicMessageCount", "_s2", "response", "getMessageCounts", "useBatchMessageCounts", "topicNames", "_s3", "sort", "join"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/hooks/useProgressiveTopicData.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useQuery, useQueryClient } from 'react-query';\nimport { topicsApi } from '../services/api';\n\n/**\n * Hook for progressive loading of topic data\n * First loads basic topic info, then loads message counts on-demand\n */\nexport const useProgressiveTopicData = (page, limit, searchTerm) => {\n  const [loadMessageCounts, setLoadMessageCounts] = useState(false);\n  const [visibleTopics, setVisibleTopics] = useState(new Set());\n  const queryClient = useQueryClient();\n\n  // Load basic topic data first (fast)\n  const basicTopicsQuery = useQuery(\n    ['topics-basic', page, limit, searchTerm],\n    () => topicsApi.getPaginated({\n      page,\n      limit,\n      search: searchTerm,\n      includeCounts: false\n    }),\n    {\n      refetchInterval: 30000,\n      keepPreviousData: true,\n      staleTime: 60000,\n    }\n  );\n\n  // Load message counts separately (slower)\n  const messageCountsQuery = useQuery(\n    ['topics-with-counts', page, limit, searchTerm],\n    () => topicsApi.getPaginated({\n      page,\n      limit,\n      search: searchTerm,\n      includeCounts: true\n    }),\n    {\n      enabled: loadMessageCounts,\n      refetchInterval: 60000, // Less frequent updates for counts\n      keepPreviousData: true,\n      staleTime: 120000, // 2 minutes\n    }\n  );\n\n  // Merge basic data with message counts when available\n  const mergedTopics = useCallback(() => {\n    const basicTopics = basicTopicsQuery.data?.data?.data || [];\n    const topicsWithCounts = messageCountsQuery.data?.data?.data || [];\n\n    if (topicsWithCounts.length > 0) {\n      // Create a map for quick lookup\n      const countsMap = new Map(\n        topicsWithCounts.map(topic => [topic.name, topic])\n      );\n\n      return basicTopics.map(topic => ({\n        ...topic,\n        totalMessages: countsMap.get(topic.name)?.totalMessages,\n        partitionDetails: countsMap.get(topic.name)?.partitionDetails || topic.partitionDetails\n      }));\n    }\n\n    return basicTopics;\n  }, [basicTopicsQuery.data, messageCountsQuery.data]);\n\n  // Auto-load message counts after basic data is loaded and user has been on page for a bit\n  useEffect(() => {\n    if (basicTopicsQuery.data && !loadMessageCounts) {\n      const timer = setTimeout(() => {\n        setLoadMessageCounts(true);\n      }, 1000); // Load counts after 1 second\n\n      return () => clearTimeout(timer);\n    }\n  }, [basicTopicsQuery.data, loadMessageCounts]);\n\n  // Track which topics are visible for lazy loading\n  const markTopicVisible = useCallback((topicName) => {\n    setVisibleTopics(prev => new Set([...prev, topicName]));\n  }, []);\n\n  // Force load message counts immediately\n  const forceLoadMessageCounts = useCallback(() => {\n    setLoadMessageCounts(true);\n  }, []);\n\n  // Refresh all data\n  const refreshData = useCallback(() => {\n    queryClient.invalidateQueries(['topics-basic']);\n    queryClient.invalidateQueries(['topics-with-counts']);\n  }, [queryClient]);\n\n  return {\n    // Data\n    topics: mergedTopics(),\n    pagination: basicTopicsQuery.data?.data?.pagination || {},\n    metadata: {\n      ...basicTopicsQuery.data?.data?.metadata,\n      messageCountsLoaded: messageCountsQuery.data !== undefined,\n      messageCountsLoading: messageCountsQuery.isLoading,\n    },\n\n    // Loading states\n    isLoading: basicTopicsQuery.isLoading,\n    isLoadingCounts: messageCountsQuery.isLoading,\n    error: basicTopicsQuery.error || messageCountsQuery.error,\n\n    // Actions\n    markTopicVisible,\n    forceLoadMessageCounts,\n    refreshData,\n\n    // State\n    messageCountsEnabled: loadMessageCounts,\n    visibleTopics,\n  };\n};\n\n/**\n * Hook for loading individual topic message count on-demand\n */\nexport const useTopicMessageCount = (topicName, enabled = false) => {\n  return useQuery(\n    ['topic-message-count', topicName],\n    async () => {\n      const response = await topicsApi.getMessageCounts([topicName]);\n      return response.data.data[0]; // Returns the first (and only) topic data\n    },\n    {\n      enabled: enabled && !!topicName,\n      staleTime: 120000, // 2 minutes\n      refetchInterval: false, // Don't auto-refetch\n    }\n  );\n};\n\n/**\n * Hook for batch loading message counts for multiple topics\n */\nexport const useBatchMessageCounts = (topicNames, enabled = false) => {\n  return useQuery(\n    ['batch-message-counts', topicNames.sort().join(',')],\n    async () => {\n      if (topicNames.length === 0) return [];\n      const response = await topicsApi.getMessageCounts(topicNames);\n      return response.data.data;\n    },\n    {\n      enabled: enabled && topicNames.length > 0,\n      staleTime: 120000, // 2 minutes\n      refetchInterval: false,\n    }\n  );\n};\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,QAAQ,EAAEC,cAAc,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,UAAU,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAImB,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAMC,WAAW,GAAGhB,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMiB,gBAAgB,GAAGlB,QAAQ,CAC/B,CAAC,cAAc,EAAEI,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC,EACzC,MAAMJ,SAAS,CAACiB,YAAY,CAAC;IAC3Bf,IAAI;IACJC,KAAK;IACLe,MAAM,EAAEd,UAAU;IAClBe,aAAa,EAAE;EACjB,CAAC,CAAC,EACF;IACEC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE;EACb,CACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGzB,QAAQ,CACjC,CAAC,oBAAoB,EAAEI,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC,EAC/C,MAAMJ,SAAS,CAACiB,YAAY,CAAC;IAC3Bf,IAAI;IACJC,KAAK;IACLe,MAAM,EAAEd,UAAU;IAClBe,aAAa,EAAE;EACjB,CAAC,CAAC,EACF;IACEK,OAAO,EAAEd,iBAAiB;IAC1BU,eAAe,EAAE,KAAK;IAAE;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,MAAM,CAAE;EACrB,CACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG5B,WAAW,CAAC,MAAM;IAAA,IAAA6B,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACrC,MAAMC,WAAW,GAAG,EAAAJ,qBAAA,GAAAV,gBAAgB,CAACe,IAAI,cAAAL,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBK,IAAI,cAAAJ,sBAAA,uBAA3BA,sBAAA,CAA6BI,IAAI,KAAI,EAAE;IAC3D,MAAMC,gBAAgB,GAAG,EAAAJ,qBAAA,GAAAL,kBAAkB,CAACQ,IAAI,cAAAH,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBG,IAAI,cAAAF,sBAAA,uBAA7BA,sBAAA,CAA+BE,IAAI,KAAI,EAAE;IAElE,IAAIC,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/B;MACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CACvBH,gBAAgB,CAACI,GAAG,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,IAAI,EAAED,KAAK,CAAC,CACnD,CAAC;MAED,OAAOP,WAAW,CAACM,GAAG,CAACC,KAAK;QAAA,IAAAE,cAAA,EAAAC,eAAA;QAAA,OAAK;UAC/B,GAAGH,KAAK;UACRI,aAAa,GAAAF,cAAA,GAAEL,SAAS,CAACQ,GAAG,CAACL,KAAK,CAACC,IAAI,CAAC,cAAAC,cAAA,uBAAzBA,cAAA,CAA2BE,aAAa;UACvDE,gBAAgB,EAAE,EAAAH,eAAA,GAAAN,SAAS,CAACQ,GAAG,CAACL,KAAK,CAACC,IAAI,CAAC,cAAAE,eAAA,uBAAzBA,eAAA,CAA2BG,gBAAgB,KAAIN,KAAK,CAACM;QACzE,CAAC;MAAA,CAAC,CAAC;IACL;IAEA,OAAOb,WAAW;EACpB,CAAC,EAAE,CAACd,gBAAgB,CAACe,IAAI,EAAER,kBAAkB,CAACQ,IAAI,CAAC,CAAC;;EAEpD;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIoB,gBAAgB,CAACe,IAAI,IAAI,CAACrB,iBAAiB,EAAE;MAC/C,MAAMkC,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlC,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMmC,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC5B,gBAAgB,CAACe,IAAI,EAAErB,iBAAiB,CAAC,CAAC;;EAE9C;EACA,MAAMqC,gBAAgB,GAAGlD,WAAW,CAAEmD,SAAS,IAAK;IAClDnC,gBAAgB,CAACoC,IAAI,IAAI,IAAInC,GAAG,CAAC,CAAC,GAAGmC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,sBAAsB,GAAGrD,WAAW,CAAC,MAAM;IAC/Cc,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwC,WAAW,GAAGtD,WAAW,CAAC,MAAM;IACpCkB,WAAW,CAACqC,iBAAiB,CAAC,CAAC,cAAc,CAAC,CAAC;IAC/CrC,WAAW,CAACqC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,CAAC;EACvD,CAAC,EAAE,CAACrC,WAAW,CAAC,CAAC;EAEjB,OAAO;IACL;IACAsC,MAAM,EAAE5B,YAAY,CAAC,CAAC;IACtB6B,UAAU,EAAE,EAAAhD,sBAAA,GAAAU,gBAAgB,CAACe,IAAI,cAAAzB,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuByB,IAAI,cAAAxB,sBAAA,uBAA3BA,sBAAA,CAA6B+C,UAAU,KAAI,CAAC,CAAC;IACzDC,QAAQ,EAAE;MACR,KAAA/C,sBAAA,GAAGQ,gBAAgB,CAACe,IAAI,cAAAvB,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBuB,IAAI,cAAAtB,sBAAA,uBAA3BA,sBAAA,CAA6B8C,QAAQ;MACxCC,mBAAmB,EAAEjC,kBAAkB,CAACQ,IAAI,KAAK0B,SAAS;MAC1DC,oBAAoB,EAAEnC,kBAAkB,CAACoC;IAC3C,CAAC;IAED;IACAA,SAAS,EAAE3C,gBAAgB,CAAC2C,SAAS;IACrCC,eAAe,EAAErC,kBAAkB,CAACoC,SAAS;IAC7CE,KAAK,EAAE7C,gBAAgB,CAAC6C,KAAK,IAAItC,kBAAkB,CAACsC,KAAK;IAEzD;IACAd,gBAAgB;IAChBG,sBAAsB;IACtBC,WAAW;IAEX;IACAW,oBAAoB,EAAEpD,iBAAiB;IACvCE;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AAFAP,EAAA,CAhHaJ,uBAAuB;EAAA,QAGdF,cAAc,EAGTD,QAAQ,EAgBNA,QAAQ;AAAA;AA6FrC,OAAO,MAAMiE,oBAAoB,GAAGA,CAACf,SAAS,EAAExB,OAAO,GAAG,KAAK,KAAK;EAAAwC,GAAA;EAClE,OAAOlE,QAAQ,CACb,CAAC,qBAAqB,EAAEkD,SAAS,CAAC,EAClC,YAAY;IACV,MAAMiB,QAAQ,GAAG,MAAMjE,SAAS,CAACkE,gBAAgB,CAAC,CAAClB,SAAS,CAAC,CAAC;IAC9D,OAAOiB,QAAQ,CAAClC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,EACD;IACEP,OAAO,EAAEA,OAAO,IAAI,CAAC,CAACwB,SAAS;IAC/B1B,SAAS,EAAE,MAAM;IAAE;IACnBF,eAAe,EAAE,KAAK,CAAE;EAC1B,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AAFA4C,GAAA,CAfaD,oBAAoB;EAAA,QACxBjE,QAAQ;AAAA;AAiBjB,OAAO,MAAMqE,qBAAqB,GAAGA,CAACC,UAAU,EAAE5C,OAAO,GAAG,KAAK,KAAK;EAAA6C,GAAA;EACpE,OAAOvE,QAAQ,CACb,CAAC,sBAAsB,EAAEsE,UAAU,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,EACrD,YAAY;IACV,IAAIH,UAAU,CAACnC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACtC,MAAMgC,QAAQ,GAAG,MAAMjE,SAAS,CAACkE,gBAAgB,CAACE,UAAU,CAAC;IAC7D,OAAOH,QAAQ,CAAClC,IAAI,CAACA,IAAI;EAC3B,CAAC,EACD;IACEP,OAAO,EAAEA,OAAO,IAAI4C,UAAU,CAACnC,MAAM,GAAG,CAAC;IACzCX,SAAS,EAAE,MAAM;IAAE;IACnBF,eAAe,EAAE;EACnB,CACF,CAAC;AACH,CAAC;AAACiD,GAAA,CAdWF,qBAAqB;EAAA,QACzBrE,QAAQ;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}