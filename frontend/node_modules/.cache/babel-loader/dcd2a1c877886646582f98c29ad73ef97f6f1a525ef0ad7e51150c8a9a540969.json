{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor\napi.interceptors.request.use(config => {\n  // Add auth headers\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  if (error.response) {\n    var _error$response$data$;\n    // Handle auth errors\n    if (error.response.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      // Only redirect if we're not already on the login page\n      if (!window.location.pathname.includes('/login')) {\n        window.location.href = '/login';\n      }\n    }\n    // Server responded with error status\n    throw new Error(error.response.data.message || ((_error$response$data$ = error.response.data.error) === null || _error$response$data$ === void 0 ? void 0 : _error$response$data$.message) || 'Server error');\n  } else if (error.request) {\n    // Request made but no response\n    throw new Error('No response from server');\n  } else {\n    // Something else happened\n    throw new Error(error.message || 'Unknown error');\n  }\n});\n\n// Topics API\nexport const topicsApi = {\n  getAll: (params = {}) => api.get('/topics', {\n    params\n  }),\n  getAllWithCounts: () => api.get('/topics', {\n    params: {\n      includeCounts: 'true'\n    }\n  }),\n  getPaginated: (params = {}) => api.get('/topics', {\n    params\n  }),\n  getById: topicName => api.get(`/topics/${topicName}`),\n  create: topicData => api.post('/topics', topicData),\n  update: (topicName, configs) => api.put(`/topics/${topicName}`, {\n    configs\n  }),\n  delete: topicName => api.delete(`/topics/${topicName}`),\n  addPartitions: (topicName, partitionCount) => api.post(`/topics/${topicName}/partitions`, {\n    partitionCount\n  }),\n  getMessages: (topicName, params) => api.get(`/topics/${topicName}/messages`, {\n    params\n  }),\n  searchMessages: (topicName, params) => api.get(`/topics/${topicName}/search`, {\n    params\n  }),\n  produceMessage: (topicName, message) => api.post(`/topics/${topicName}/messages`, message),\n  subscribe: topicName => api.post(`/topics/${topicName}/subscribe`),\n  unsubscribe: topicName => api.post(`/topics/${topicName}/unsubscribe`),\n  // New methods for on-demand message count loading\n  getMessageCount: topicName => api.get(`/topics/${topicName}/message-count`),\n  getMessageCounts: topicNames => api.post('/topics/message-counts', {\n    topicNames\n  })\n};\n\n// Consumer Groups API\nexport const consumerGroupsApi = {\n  getAll: () => api.get('/consumers'),\n  getById: groupId => api.get(`/consumers/${groupId}`),\n  delete: groupId => api.delete(`/consumers/${groupId}`)\n};\n\n// Producers API\nexport const producersApi = {\n  bulkProduce: data => api.post('/producers/bulk', data)\n};\n\n// Cluster API\nexport const clusterApi = {\n  getInfo: () => api.get('/cluster/info'),\n  getHealth: () => api.get('/cluster/health'),\n  getMessageFlow: () => api.get('/cluster/message-flow'),\n  getBrokerMetrics: () => api.get('/cluster/broker-metrics'),\n  getMessageRate: () => api.get('/cluster/message-rate')\n};\n\n// Config API\nexport const configApi = {\n  get: () => api.get('/config'),\n  getKafkaStatus: () => api.get('/config/kafka-status')\n};\n\n// Auth API\nexport const authApi = {\n  login: credentials => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  getProfile: () => api.get('/auth/profile'),\n  updateProfile: profileData => api.put('/auth/profile', profileData),\n  changePassword: passwordData => api.put('/auth/change-password', passwordData),\n  register: userData => api.post('/auth/register', userData),\n  setup: adminData => api.post('/auth/setup', adminData),\n  getUsers: () => api.get('/auth/users'),\n  updateUser: (userId, userData) => api.put(`/auth/users/${userId}`, userData),\n  deleteUser: userId => api.delete(`/auth/users/${userId}`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response$data$", "status", "removeItem", "window", "location", "pathname", "includes", "href", "Error", "message", "topicsApi", "getAll", "params", "get", "getAllWithCounts", "includeCounts", "getPaginated", "getById", "topicName", "topicData", "post", "update", "configs", "put", "delete", "addPartitions", "partitionCount", "getMessages", "searchMessages", "produceMessage", "subscribe", "unsubscribe", "getMessageCount", "getMessageCounts", "topicNames", "consumerGroupsApi", "groupId", "producersApi", "bulkProduce", "clusterApi", "getInfo", "getHealth", "getMessageFlow", "getBrokerMetrics", "getMessageRate", "config<PERSON>pi", "getKafkaStatus", "authApi", "login", "credentials", "logout", "getProfile", "updateProfile", "profileData", "changePassword", "passwordData", "register", "userData", "setup", "adminData", "getUsers", "updateUser", "userId", "deleteUser"], "sources": ["/home/<USER>/Projects/Kafka-dashboard-COPY070717/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    // Add auth headers\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // Handle auth errors\n      if (error.response.status === 401) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        // Only redirect if we're not already on the login page\n        if (!window.location.pathname.includes('/login')) {\n          window.location.href = '/login';\n        }\n      }\n      // Server responded with error status\n      throw new Error(error.response.data.message || error.response.data.error?.message || 'Server error');\n    } else if (error.request) {\n      // Request made but no response\n      throw new Error('No response from server');\n    } else {\n      // Something else happened\n      throw new Error(error.message || 'Unknown error');\n    }\n  }\n);\n\n// Topics API\nexport const topicsApi = {\n  getAll: (params = {}) => api.get('/topics', { params }),\n  getAllWithCounts: () => api.get('/topics', { params: { includeCounts: 'true' } }),\n  getPaginated: (params = {}) => api.get('/topics', { params }),\n  getById: (topicName) => api.get(`/topics/${topicName}`),\n  create: (topicData) => api.post('/topics', topicData),\n  update: (topicName, configs) => api.put(`/topics/${topicName}`, { configs }),\n  delete: (topicName) => api.delete(`/topics/${topicName}`),\n  addPartitions: (topicName, partitionCount) => \n    api.post(`/topics/${topicName}/partitions`, { partitionCount }),\n  getMessages: (topicName, params) => \n    api.get(`/topics/${topicName}/messages`, { params }),\n  searchMessages: (topicName, params) => \n    api.get(`/topics/${topicName}/search`, { params }),\n  produceMessage: (topicName, message) => \n    api.post(`/topics/${topicName}/messages`, message),\n  subscribe: (topicName) => api.post(`/topics/${topicName}/subscribe`),\n  unsubscribe: (topicName) => api.post(`/topics/${topicName}/unsubscribe`),\n  // New methods for on-demand message count loading\n  getMessageCount: (topicName) => api.get(`/topics/${topicName}/message-count`),\n  getMessageCounts: (topicNames) => api.post('/topics/message-counts', { topicNames }),\n};\n\n// Consumer Groups API\nexport const consumerGroupsApi = {\n  getAll: () => api.get('/consumers'),\n  getById: (groupId) => api.get(`/consumers/${groupId}`),\n  delete: (groupId) => api.delete(`/consumers/${groupId}`),\n};\n\n// Producers API\nexport const producersApi = {\n  bulkProduce: (data) => api.post('/producers/bulk', data),\n};\n\n// Cluster API\nexport const clusterApi = {\n  getInfo: () => api.get('/cluster/info'),\n  getHealth: () => api.get('/cluster/health'),\n  getMessageFlow: () => api.get('/cluster/message-flow'),\n  getBrokerMetrics: () => api.get('/cluster/broker-metrics'),\n  getMessageRate: () => api.get('/cluster/message-rate'),\n};\n\n// Config API\nexport const configApi = {\n  get: () => api.get('/config'),\n  getKafkaStatus: () => api.get('/config/kafka-status'),\n};\n\n// Auth API\nexport const authApi = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  getProfile: () => api.get('/auth/profile'),\n  updateProfile: (profileData) => api.put('/auth/profile', profileData),\n  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),\n  register: (userData) => api.post('/auth/register', userData),\n  setup: (adminData) => api.post('/auth/setup', adminData),\n  getUsers: () => api.get('/auth/users'),\n  updateUser: (userId, userData) => api.put(`/auth/users/${userId}`, userData),\n  deleteUser: (userId) => api.delete(`/auth/users/${userId}`),\n};\n\nexport default api; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EACT,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAAA,IAAAE,qBAAA;IAClB;IACA,IAAIL,KAAK,CAACG,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;MACjCT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;MAC/B;MACA,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAChDH,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,QAAQ;MACjC;IACF;IACA;IACA,MAAM,IAAIC,KAAK,CAACb,KAAK,CAACG,QAAQ,CAACC,IAAI,CAACU,OAAO,MAAAT,qBAAA,GAAIL,KAAK,CAACG,QAAQ,CAACC,IAAI,CAACJ,KAAK,cAAAK,qBAAA,uBAAzBA,qBAAA,CAA2BS,OAAO,KAAI,cAAc,CAAC;EACtG,CAAC,MAAM,IAAId,KAAK,CAACP,OAAO,EAAE;IACxB;IACA,MAAM,IAAIoB,KAAK,CAAC,yBAAyB,CAAC;EAC5C,CAAC,MAAM;IACL;IACA,MAAM,IAAIA,KAAK,CAACb,KAAK,CAACc,OAAO,IAAI,eAAe,CAAC;EACnD;AACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK9B,GAAG,CAAC+B,GAAG,CAAC,SAAS,EAAE;IAAED;EAAO,CAAC,CAAC;EACvDE,gBAAgB,EAAEA,CAAA,KAAMhC,GAAG,CAAC+B,GAAG,CAAC,SAAS,EAAE;IAAED,MAAM,EAAE;MAAEG,aAAa,EAAE;IAAO;EAAE,CAAC,CAAC;EACjFC,YAAY,EAAEA,CAACJ,MAAM,GAAG,CAAC,CAAC,KAAK9B,GAAG,CAAC+B,GAAG,CAAC,SAAS,EAAE;IAAED;EAAO,CAAC,CAAC;EAC7DK,OAAO,EAAGC,SAAS,IAAKpC,GAAG,CAAC+B,GAAG,CAAC,WAAWK,SAAS,EAAE,CAAC;EACvDnC,MAAM,EAAGoC,SAAS,IAAKrC,GAAG,CAACsC,IAAI,CAAC,SAAS,EAAED,SAAS,CAAC;EACrDE,MAAM,EAAEA,CAACH,SAAS,EAAEI,OAAO,KAAKxC,GAAG,CAACyC,GAAG,CAAC,WAAWL,SAAS,EAAE,EAAE;IAAEI;EAAQ,CAAC,CAAC;EAC5EE,MAAM,EAAGN,SAAS,IAAKpC,GAAG,CAAC0C,MAAM,CAAC,WAAWN,SAAS,EAAE,CAAC;EACzDO,aAAa,EAAEA,CAACP,SAAS,EAAEQ,cAAc,KACvC5C,GAAG,CAACsC,IAAI,CAAC,WAAWF,SAAS,aAAa,EAAE;IAAEQ;EAAe,CAAC,CAAC;EACjEC,WAAW,EAAEA,CAACT,SAAS,EAAEN,MAAM,KAC7B9B,GAAG,CAAC+B,GAAG,CAAC,WAAWK,SAAS,WAAW,EAAE;IAAEN;EAAO,CAAC,CAAC;EACtDgB,cAAc,EAAEA,CAACV,SAAS,EAAEN,MAAM,KAChC9B,GAAG,CAAC+B,GAAG,CAAC,WAAWK,SAAS,SAAS,EAAE;IAAEN;EAAO,CAAC,CAAC;EACpDiB,cAAc,EAAEA,CAACX,SAAS,EAAET,OAAO,KACjC3B,GAAG,CAACsC,IAAI,CAAC,WAAWF,SAAS,WAAW,EAAET,OAAO,CAAC;EACpDqB,SAAS,EAAGZ,SAAS,IAAKpC,GAAG,CAACsC,IAAI,CAAC,WAAWF,SAAS,YAAY,CAAC;EACpEa,WAAW,EAAGb,SAAS,IAAKpC,GAAG,CAACsC,IAAI,CAAC,WAAWF,SAAS,cAAc,CAAC;EACxE;EACAc,eAAe,EAAGd,SAAS,IAAKpC,GAAG,CAAC+B,GAAG,CAAC,WAAWK,SAAS,gBAAgB,CAAC;EAC7Ee,gBAAgB,EAAGC,UAAU,IAAKpD,GAAG,CAACsC,IAAI,CAAC,wBAAwB,EAAE;IAAEc;EAAW,CAAC;AACrF,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAG;EAC/BxB,MAAM,EAAEA,CAAA,KAAM7B,GAAG,CAAC+B,GAAG,CAAC,YAAY,CAAC;EACnCI,OAAO,EAAGmB,OAAO,IAAKtD,GAAG,CAAC+B,GAAG,CAAC,cAAcuB,OAAO,EAAE,CAAC;EACtDZ,MAAM,EAAGY,OAAO,IAAKtD,GAAG,CAAC0C,MAAM,CAAC,cAAcY,OAAO,EAAE;AACzD,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,WAAW,EAAGvC,IAAI,IAAKjB,GAAG,CAACsC,IAAI,CAAC,iBAAiB,EAAErB,IAAI;AACzD,CAAC;;AAED;AACA,OAAO,MAAMwC,UAAU,GAAG;EACxBC,OAAO,EAAEA,CAAA,KAAM1D,GAAG,CAAC+B,GAAG,CAAC,eAAe,CAAC;EACvC4B,SAAS,EAAEA,CAAA,KAAM3D,GAAG,CAAC+B,GAAG,CAAC,iBAAiB,CAAC;EAC3C6B,cAAc,EAAEA,CAAA,KAAM5D,GAAG,CAAC+B,GAAG,CAAC,uBAAuB,CAAC;EACtD8B,gBAAgB,EAAEA,CAAA,KAAM7D,GAAG,CAAC+B,GAAG,CAAC,yBAAyB,CAAC;EAC1D+B,cAAc,EAAEA,CAAA,KAAM9D,GAAG,CAAC+B,GAAG,CAAC,uBAAuB;AACvD,CAAC;;AAED;AACA,OAAO,MAAMgC,SAAS,GAAG;EACvBhC,GAAG,EAAEA,CAAA,KAAM/B,GAAG,CAAC+B,GAAG,CAAC,SAAS,CAAC;EAC7BiC,cAAc,EAAEA,CAAA,KAAMhE,GAAG,CAAC+B,GAAG,CAAC,sBAAsB;AACtD,CAAC;;AAED;AACA,OAAO,MAAMkC,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IAAKnE,GAAG,CAACsC,IAAI,CAAC,aAAa,EAAE6B,WAAW,CAAC;EAC5DC,MAAM,EAAEA,CAAA,KAAMpE,GAAG,CAACsC,IAAI,CAAC,cAAc,CAAC;EACtC+B,UAAU,EAAEA,CAAA,KAAMrE,GAAG,CAAC+B,GAAG,CAAC,eAAe,CAAC;EAC1CuC,aAAa,EAAGC,WAAW,IAAKvE,GAAG,CAACyC,GAAG,CAAC,eAAe,EAAE8B,WAAW,CAAC;EACrEC,cAAc,EAAGC,YAAY,IAAKzE,GAAG,CAACyC,GAAG,CAAC,uBAAuB,EAAEgC,YAAY,CAAC;EAChFC,QAAQ,EAAGC,QAAQ,IAAK3E,GAAG,CAACsC,IAAI,CAAC,gBAAgB,EAAEqC,QAAQ,CAAC;EAC5DC,KAAK,EAAGC,SAAS,IAAK7E,GAAG,CAACsC,IAAI,CAAC,aAAa,EAAEuC,SAAS,CAAC;EACxDC,QAAQ,EAAEA,CAAA,KAAM9E,GAAG,CAAC+B,GAAG,CAAC,aAAa,CAAC;EACtCgD,UAAU,EAAEA,CAACC,MAAM,EAAEL,QAAQ,KAAK3E,GAAG,CAACyC,GAAG,CAAC,eAAeuC,MAAM,EAAE,EAAEL,QAAQ,CAAC;EAC5EM,UAAU,EAAGD,MAAM,IAAKhF,GAAG,CAAC0C,MAAM,CAAC,eAAesC,MAAM,EAAE;AAC5D,CAAC;AAED,eAAehF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}