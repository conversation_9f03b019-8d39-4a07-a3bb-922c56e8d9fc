{"name": "kafka-dashboard-backend", "version": "1.0.0", "description": "Backend API for Kafka Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "start:local": "NODE_ENV=local node server.js", "start:qa": "NODE_ENV=qa node server.js", "start:prod": "NODE_ENV=prod node server.js", "dev:local": "NODE_ENV=local nodemon server.js", "dev:qa": "NODE_ENV=qa nodemon server.js", "dev:prod": "NODE_ENV=prod nodemon server.js", "test": "jest"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.2.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "mongoose": "^8.16.1", "snappy": "^7.2.2", "socket.io": "^4.7.2", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.1", "nodemon": "^3.0.1"}, "keywords": ["kafka", "api", "dashboard", "nodejs"], "author": "PolicyBazaar", "license": "MIT"}