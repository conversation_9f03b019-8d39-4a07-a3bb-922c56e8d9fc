const express = require('express');
const router = express.Router();
const kafkaClient = require('../kafka/kafkaClient');
const logger = require('../utils/logger');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');

// Rate limiting for topic operations
const topicRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 requests per windowMs
  message: { error: 'Too many topic operations, please try again later' }
});

// Message rate limiting
const messageRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // limit each IP to 100 message requests per windowMs
  message: { error: 'Too many message requests, please try again later' }
});

// GET /api/topics - Get all topics
router.get('/', async (req, res) => {
  try {
    const { includeCounts } = req.query;
    
    let topics;
    if (includeCounts === 'true') {
      topics = await kafkaClient.getTopicsWithMessageCounts();
    } else {
      topics = await kafkaClient.getTopics();
    }
    
    res.json({
      success: true,
      data: topics
    });
  } catch (error) {
    logger.error('Error fetching topics:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topics',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName - Get topic details
router.get('/:topicName', async (req, res) => {
  try {
    const { topicName } = req.params;
    const topics = await kafkaClient.getTopics();
    const topic = topics.find(t => t.name === topicName);
    
    if (!topic) {
      return res.status(404).json({
        error: {
          message: 'Topic not found',
          status: 404
        }
      });
    }
    
    res.json({
      success: true,
      data: topic
    });
  } catch (error) {
    logger.error('Error fetching topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topic',
        status: 500
      }
    });
  }
});

// POST /api/topics - Create a new topic
router.post('/', topicRateLimit, [
  body('name').isString().isLength({ min: 1 }).withMessage('Topic name is required'),
  body('numPartitions').isInt({ min: 1 }).withMessage('Number of partitions must be at least 1'),
  body('replicationFactor').isInt({ min: 1 }).withMessage('Replication factor must be at least 1'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array(),
          status: 400
        }
      });
    }

    const result = await kafkaClient.createTopic(req.body);
    res.status(201).json(result);
  } catch (error) {
    logger.error('Error creating topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to create topic',
        status: 500
      }
    });
  }
});

// DELETE /api/topics/:topicName - Delete a topic
router.delete('/:topicName', topicRateLimit, async (req, res) => {
  try {
    const { topicName } = req.params;
    const result = await kafkaClient.deleteTopic(topicName);
    res.json(result);
  } catch (error) {
    logger.error('Error deleting topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to delete topic',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName/messages - Get messages from a topic
router.get('/:topicName/messages', async (req, res) => {
  try {
    const { topicName } = req.params;
    const { partition = -1, offset = 0, limit = 100, startFrom = 'latest' } = req.query;
    
    // Validate parameters
    const parsedLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 messages
    const parsedPartition = parseInt(partition) || -1;
    
    logger.info(`Fetching messages from topic: ${topicName}, partition: ${parsedPartition}, limit: ${parsedLimit}, startFrom: ${startFrom}`);
    
    let messages;
    
    // Use different methods based on the startFrom parameter
    if (startFrom === 'latest' || startFrom === 'earliest') {
      messages = await kafkaClient.getMessagesFromOffset(topicName, parsedPartition, startFrom, parsedLimit);
    } else {
      messages = await kafkaClient.getMessages(topicName, parsedPartition, parseInt(offset) || 0, parsedLimit);
    }
    
    // Add metadata about the request
    const response = {
      success: true,
      data: messages,
      metadata: {
        topicName,
        partition: parsedPartition,
        limit: parsedLimit,
        startFrom,
        messageCount: messages.length,
        timestamp: new Date().toISOString()
      }
    };
    
    res.json(response);
  } catch (error) {
    logger.error(`Error fetching messages for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: [] // Return empty array instead of failing completely
    });
  }
});

// POST /api/topics/:topicName/messages - Produce a message to a topic
router.post('/:topicName/messages', messageRateLimit, [
  body('value').isString().withMessage('Message value is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array(),
          status: 400
        }
      });
    }

    const { topicName } = req.params;
    const result = await kafkaClient.produceMessage(topicName, req.body);
    res.json(result);
  } catch (error) {
    logger.error('Error producing message:', error);
    res.status(500).json({
      error: {
        message: 'Failed to produce message',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/partitions - Add partitions to a topic
router.post('/:topicName/partitions', topicRateLimit, [
  body('partitionCount').isInt({ min: 1 }).withMessage('Partition count must be at least 1'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array(),
          status: 400
        }
      });
    }

    const { topicName } = req.params;
    const { partitionCount } = req.body;
    const result = await kafkaClient.addPartitions(topicName, partitionCount);
    res.json(result);
  } catch (error) {
    logger.error('Error adding partitions:', error);
    res.status(500).json({
      error: {
        message: 'Failed to add partitions',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName/config - Get topic configuration
router.get('/:topicName/config', async (req, res) => {
  try {
    const { topicName } = req.params;
    const config = await kafkaClient.getTopicConfig(topicName);
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    logger.error('Error fetching topic config:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topic configuration',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/subscribe - Subscribe to real-time messages
router.post('/:topicName/subscribe', async (req, res) => {
  try {
    const { topicName } = req.params;
    
    // Get global io instance
    const io = global.io;
    
    if (!io) {
      return res.status(500).json({
        error: {
          message: 'Socket.IO not available',
          status: 500
        }
      });
    }
    
    // Start real-time consumer
    const result = await kafkaClient.startRealtimeConsumer(topicName, (messageData) => {
      // Emit message to all clients subscribed to this topic
      io.to(`topic-${topicName}`).emit('message', messageData);
    });
    
    res.json(result);
  } catch (error) {
    logger.error('Error subscribing to topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to subscribe to topic',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/unsubscribe - Unsubscribe from real-time messages
router.post('/:topicName/unsubscribe', async (req, res) => {
  try {
    const { topicName } = req.params;
    
    const result = await kafkaClient.stopRealtimeConsumer(topicName);
    
    res.json(result);
  } catch (error) {
    logger.error('Error unsubscribing from topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to unsubscribe from topic',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName/message-count - Get message count for a specific topic
router.get('/:topicName/message-count', async (req, res) => {
  try {
    const { topicName } = req.params;
    const messageCount = await kafkaClient.getTopicMessageCount(topicName);
    
    res.json({
      success: true,
      data: messageCount
    });
  } catch (error) {
    logger.error(`Error fetching message count for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// GET /api/topics/:topicName/search - Search messages with filters
router.get('/:topicName/search', messageRateLimit, async (req, res) => {
  try {
    const { topicName } = req.params;
    const { 
      partition = -1, 
      limit = 100, 
      startFrom = 'latest',
      key = '',
      value = '',
      startTimestamp = '',
      endTimestamp = '',
      caseSensitive = 'false'
    } = req.query;
    
    // Validate parameters
    const parsedLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 messages
    const parsedPartition = parseInt(partition) || -1;
    const isCaseSensitive = caseSensitive === 'true';
    
    logger.info(`Searching messages in topic: ${topicName}, partition: ${parsedPartition}, limit: ${parsedLimit}, startFrom: ${startFrom}`);
    logger.info(`Search filters - key: "${key}", value: "${value}", startTimestamp: ${startTimestamp}, endTimestamp: ${endTimestamp}, caseSensitive: ${isCaseSensitive}`);
    
    // Parse timestamps if provided
    let startTime = null;
    let endTime = null;
    
    if (startTimestamp) {
      startTime = new Date(startTimestamp).getTime();
      if (isNaN(startTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid startTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    if (endTimestamp) {
      endTime = new Date(endTimestamp).getTime();
      if (isNaN(endTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid endTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    // Validate timestamp range
    if (startTime && endTime && startTime > endTime) {
      return res.status(400).json({
        success: false,
        error: 'startTimestamp cannot be after endTimestamp',
        data: []
      });
    }
    
    const searchFilters = {
      key: key.trim(),
      value: value.trim(),
      startTimestamp: startTime,
      endTimestamp: endTime,
      caseSensitive: isCaseSensitive
    };
    
    const messages = await kafkaClient.searchMessages(
      topicName, 
      parsedPartition, 
      startFrom, 
      parsedLimit, 
      searchFilters
    );
    
    // Add metadata about the search
    const response = {
      success: true,
      data: messages,
      metadata: {
        topicName,
        partition: parsedPartition,
        limit: parsedLimit,
        startFrom,
        searchFilters,
        messageCount: messages.length,
        timestamp: new Date().toISOString()
      }
    };
    
    res.json(response);
  } catch (error) {
    logger.error(`Error searching messages for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: []
    });
  }
});

// POST /api/topics/message-counts - Get message counts for multiple topics
router.post('/message-counts', async (req, res) => {
  try {
    const { topicNames } = req.body;
    
    if (!Array.isArray(topicNames)) {
      return res.status(400).json({
        success: false,
        error: 'topicNames must be an array'
      });
    }

    // Limit to prevent abuse
    if (topicNames.length > 50) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 50 topics allowed per request'
      });
    }

    const messageCounts = await kafkaClient.getTopicsMessageCounts(topicNames);
    
    res.json({
      success: true,
      data: messageCounts
    });
  } catch (error) {
    logger.error('Error fetching message counts for topics:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// GET /api/topics/:topicName/search - Search messages with filters
router.get('/:topicName/search', messageRateLimit, async (req, res) => {
  try {
    const { topicName } = req.params;
    const { 
      partition = -1, 
      limit = 100, 
      startFrom = 'latest',
      key = '',
      value = '',
      startTimestamp = '',
      endTimestamp = '',
      caseSensitive = 'false'
    } = req.query;
    
    // Validate parameters
    const parsedLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 messages
    const parsedPartition = parseInt(partition) || -1;
    const isCaseSensitive = caseSensitive === 'true';
    
    logger.info(`Searching messages in topic: ${topicName}, partition: ${parsedPartition}, limit: ${parsedLimit}, startFrom: ${startFrom}`);
    logger.info(`Search filters - key: "${key}", value: "${value}", startTimestamp: ${startTimestamp}, endTimestamp: ${endTimestamp}, caseSensitive: ${isCaseSensitive}`);
    
    // Parse timestamps if provided
    let startTime = null;
    let endTime = null;
    
    if (startTimestamp) {
      startTime = new Date(startTimestamp).getTime();
      if (isNaN(startTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid startTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    if (endTimestamp) {
      endTime = new Date(endTimestamp).getTime();
      if (isNaN(endTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid endTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    // Validate timestamp range
    if (startTime && endTime && startTime > endTime) {
      return res.status(400).json({
        success: false,
        error: 'startTimestamp cannot be after endTimestamp',
        data: []
      });
    }
    
    const searchFilters = {
      key: key.trim(),
      value: value.trim(),
      startTimestamp: startTime,
      endTimestamp: endTime,
      caseSensitive: isCaseSensitive
    };
    
    const messages = await kafkaClient.searchMessages(
      topicName, 
      parsedPartition, 
      startFrom, 
      parsedLimit, 
      searchFilters
    );
    
    // Add metadata about the search
    const response = {
      success: true,
      data: messages,
      metadata: {
        topicName,
        partition: parsedPartition,
        limit: parsedLimit,
        startFrom,
        searchFilters,
        messageCount: messages.length,
        timestamp: new Date().toISOString()
      }
    };
    
    res.json(response);
  } catch (error) {
    logger.error(`Error searching messages for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: []
    });
  }
});

module.exports = router; 